{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^3.3.4", "@repo/ui": "workspace:*", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.49.3", "tailwind-merge": "^3.3.0", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.21", "eslint": "^9.26.0", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "typescript": "5.8.2"}}