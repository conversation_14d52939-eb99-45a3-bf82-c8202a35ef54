import { getRequestConfig } from "next-intl/server";
import { createRequestConfig, locales, pathnames, isValidLocale } from "./config";
import type { Locale, Pathnames } from "./config";

// Re-export all i18n configuration from the central config file
export { locales, pathnames, isValidLocale };
export type { Locale, Pathnames };

// Load messages for the requested locale using the shared config creator
export default getRequestConfig(async ({ locale }) => {
  const loadMessages = async (localeStr: string) => {
    try {
      return (await import(`../messages/${localeStr}/common.json`)).default;
    } catch (error) {
      console.error(`Failed to load messages for locale: ${localeStr}`, error);
      throw error;
    }
  };
  
  return createRequestConfig(loadMessages)({ locale });
});
