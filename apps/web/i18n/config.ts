import { notFound } from "next/navigation";

// Define the locales that your app supports
export const locales = ["en", "th"] as const;

export type Locale = (typeof locales)[number];

export type Pathnames = {
  [key: string]: {
    [key in Locale]: string;
  };
};

// Define pathnames for internationalized routes
export const pathnames: Pathnames = {
  "/": {
    en: "/",
    th: "/",
  },
  // Add more paths here as needed
};

// Helper function to check if a locale is valid
export function isValidLocale(locale: string): locale is Locale {
  return (locales as readonly string[]).includes(locale);
}

// Create a request config function that can be used by both server and client
export function createRequestConfig(
  messagesImportFn: (locale: string) => Promise<Record<string, unknown>>
) {
  return async ({ locale: localeParam }: { locale: string | undefined }) => {
    // Ensure locale is a valid string
    const locale = typeof localeParam === "string" ? localeParam : "en";

    // Validate that the incoming `locale` parameter is valid
    if (!locales.includes(locale as Locale)) notFound();

    let messages;
    try {
      messages = await messagesImportFn(locale);
    } catch (error) {
      console.error(`Failed to load messages for locale: ${locale}`, error);
      notFound();
    }

    return {
      locale,
      messages,
      now: new Date(),
      timeZone: "Asia/Bangkok",
    };
  };
}
