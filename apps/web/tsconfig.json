{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/styles/*": ["./styles/*"], "@/types/*": ["./types/*"], "@/app/*": ["./app/*"], "@/hooks/*": ["./hooks/*"], "@/store/*": ["./store/*"]}}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "next.config.js", ".next/types/**/*.ts"], "exclude": ["node_modules"]}