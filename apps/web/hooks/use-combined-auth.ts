"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

interface CombinedUser {
  id: string;
  email: string;
  name?: string;
  image?: string;
  provider?: string;
  providerId?: string;
}

interface CombinedAuthState {
  user: CombinedUser | null;
  status: "loading" | "authenticated" | "unauthenticated";
  isAuthenticated: boolean;
}

export function useCombinedAuth(): CombinedAuthState {
  const { data: session, status: nextAuthStatus } = useSession();
  const [localStorageUser, setLocalStorageUser] = useState<CombinedUser | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  // Load user data from localStorage on mount
  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setLocalStorageUser({
          id: parsedUser.id,
          email: parsedUser.email,
          name: parsedUser.name,
          image: parsedUser.picture,
          provider: parsedUser.provider,
          providerId: parsedUser.providerId,
        });
      } catch (e) {
        console.error("Failed to parse user data from localStorage:", e);
        localStorage.removeItem("user"); // Clean up invalid data
      }
    }
    setIsLoading(false);
  }, []);

  // Determine the final auth state
  const getAuthState = (): CombinedAuthState => {
    // If NextAuth is still loading, show loading
    if (nextAuthStatus === "loading" || isLoading) {
      return {
        user: null,
        status: "loading",
        isAuthenticated: false,
      };
    }

    // If we have a NextAuth session, use it
    if (nextAuthStatus === "authenticated" && session?.user) {
      // If we also have localStorage data, merge it for better user info
      if (localStorageUser && localStorageUser.email === session.user.email) {
        return {
          user: {
            id: localStorageUser.id,
            email: session.user.email || "",
            name: localStorageUser.name || session.user.name || undefined,
            image: localStorageUser.image || session.user.image || undefined,
            provider: localStorageUser.provider,
            providerId: localStorageUser.providerId,
          },
          status: "authenticated",
          isAuthenticated: true,
        };
      }

      // Use NextAuth session data
      return {
        user: {
          id: session.user.email || "unknown",
          email: session.user.email || "",
          name: session.user.name || undefined,
          image: session.user.image || undefined,
        },
        status: "authenticated",
        isAuthenticated: true,
      };
    }

    // If we have localStorage data but no NextAuth session, use localStorage
    if (localStorageUser) {
      return {
        user: localStorageUser,
        status: "authenticated",
        isAuthenticated: true,
      };
    }

    // No authentication
    return {
      user: null,
      status: "unauthenticated",
      isAuthenticated: false,
    };
  };

  return getAuthState();
}
