import * as React from "react";
import {
  QueryKey,
  useQuery,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

type UseApiOptions<TData = unknown, TError = Error> = Omit<
  UseQueryOptions<TData, TError, TData, QueryKey>,
  "queryKey" | "queryFn" | "onSuccess"
> & {
  queryKey: QueryKey;
  queryFn: () => Promise<TData>;
  onSuccess?: (data: TData) => void;
};

export function useApi<TData = unknown, TError = Error>(
  options: UseApiOptions<TData, TError>
): UseQueryResult<TData, TError> {
  const { onSuccess, ...queryOptions } = options;

  const queryResult = useQuery({
    staleTime: 1000 * 60 * 5, // 5 minutes
    ...queryOptions,
  });

  // Handle onSuccess callback using an effect
  React.useEffect(() => {
    if (onSuccess && queryResult.isSuccess && queryResult.data) {
      onSuccess(queryResult.data);
    }
  }, [onSuccess, queryResult.isSuccess, queryResult.data]);

  return queryResult;
}

// Example usage:
// const { data, isLoading, error } = useApi({
//   queryKey: ['user', userId],
//   queryFn: () => fetchUser(userId),
// });
