import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import LinkedInProvider from "next-auth/providers/linkedin";
import CredentialsProvider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: "credentials",
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Demo credentials for admin
        if (
          credentials.email === "<EMAIL>" &&
          credentials.password === "admin123"
        ) {
          return {
            id: "admin-demo",
            email: "<EMAIL>",
            name: "System Admin",
            image: null,
          };
        }

        // Handle OAuth users (from custom backend flow)
        if (credentials.password === "oauth-user") {
          // For OAuth users, we'll create a session with the email
          // The actual user data will be managed separately
          return {
            id: credentials.email,
            email: credentials.email,
            name: "<PERSON>Auth User", // This will be updated by the client
            image: null,
          };
        }

        // TODO: Add real authentication logic here
        // You can make an API call to your backend to verify credentials

        return null;
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    LinkedInProvider({
      clientId: process.env.LINKEDIN_CLIENT_ID!,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET!,
      authorization: {
        url: "https://www.linkedin.com/oauth/v2/authorization",
        params: {
          scope: "profile email openid",
        },
      },
      jwks_endpoint: "https://www.linkedin.com/oauth/openid/jwks",
      issuer: "https://www.linkedin.com/oauth",
      token: {
        url: "https://www.linkedin.com/oauth/v2/accessToken",
      },
      profile(profile, tokens) {
        return {
          id: profile.sub ?? profile.id ?? tokens.id_token,
          name:
            profile.name ??
            `${profile.given_name ?? ""} ${profile.family_name ?? ""}`.trim(),
          email: profile.email ?? profile.emailAddress,
          image:
            profile.picture ??
            profile.profilePicture?.["displayImage~"]?.elements?.[0]
              ?.identifiers?.[0]?.identifier,
        };
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (!account || !user) return false;

      // Allow credentials provider (for admin login)
      if (account.provider === "credentials") {
        return true;
      }

      try {
        const provider =
          account.provider === "linkedin" ? "linkedin" : "google";
        const apiUrl =
          process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:4000";

        const signupResponse = await fetch(
          `${apiUrl}/auth/${provider}/signup`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: user.email,
              name: user.name,
              provider: provider,
              providerId: account.providerAccountId,
              picture: user.image,
            }),
          }
        );

        if (signupResponse.ok) {
          return true;
        } else {
          const signinResponse = await fetch(
            `${apiUrl}/auth/${provider}/signin`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                providerId: account.providerAccountId,
              }),
            }
          );

          if (signinResponse.ok) {
            return true;
          } else {
            return false;
          }
        }
      } catch (error) {
        console.error("Error in signIn callback:", error);
        return false;
      }
    },
    async jwt({ token, user, account }) {
      if (account && user) {
        token.accessToken = account.access_token;
        token.providerId = account.providerAccountId;
        token.provider = account.provider;
      }
      return token;
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken as string;
      session.providerId = token.providerId as string;
      session.provider = token.provider as string;
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
};
