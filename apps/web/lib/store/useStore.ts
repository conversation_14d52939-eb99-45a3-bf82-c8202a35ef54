import { create } from "zustand";
import { devtools } from "zustand/middleware";

type User = {
  id: string | null;
  role: "employee" | "employer" | "admin" | null;
  email: string | null;
};

interface AppState {
  // Auth state
  user: User | null;
  // UI state
  theme: "light" | "dark";
  // Actions
  setUser: (user: User | null) => void;
  setTheme: (theme: "light" | "dark") => void;
  reset: () => void;
}

const initialState: Omit<AppState, "setUser" | "setTheme" | "reset"> = {
  user: null,
  theme: "light",
};

export const useStore = create<AppState>()(
  devtools(
    (set) => ({
      ...initialState,
      setUser: (user) => set({ user }),
      setTheme: (theme) => set({ theme }),
      reset: () => set(initialState),
    }),
    {
      name: "app-storage",
    }
  )
);

// Selectors
export const useUser = (): User | null => useStore((state) => state.user);
export const useTheme = (): "light" | "dark" =>
  useStore((state) => state.theme);

// Actions
type UserActions = {
  setUser: (user: User | null) => void;
  reset: () => void;
};

export const useUserActions = (): UserActions =>
  useStore((state) => ({
    setUser: state.setUser,
    reset: state.reset,
  }));
