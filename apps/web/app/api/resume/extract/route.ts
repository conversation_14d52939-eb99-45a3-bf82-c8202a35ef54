import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("resume") as File;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Forward the file to the backend API
    const backendFormData = new FormData();
    backendFormData.append("resume", file);

    const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";
    const response = await fetch(`${apiUrl}/resume/extract`, {
      method: "POST",
      body: backendFormData,
    });

    if (!response.ok) {
      throw new Error("Failed to extract resume data");
    }

    const extractedData = await response.json();
    return NextResponse.json(extractedData);
  } catch (error) {
    console.error("Error extracting resume data:", error);
    return NextResponse.json(
      { error: "Failed to extract resume data" },
      { status: 500 }
    );
  }
}
