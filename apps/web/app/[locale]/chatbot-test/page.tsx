"use client";

import { FloatingChatbot } from "@/components/organisms";

export default function ChatbotTestPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Chatbot Test Page</h1>
        <div className="space-y-4">
          <p className="text-muted-foreground">
            This page is for testing the floating chatbot. Look for the chat icon in the bottom-left corner.
          </p>
          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Features:</h2>
            <ul className="space-y-2 text-sm">
              <li>• Floating chat button in bottom-left corner</li>
              <li>• Click to open/close chat window</li>
              <li>• Powered by OpenAI GPT-3.5-turbo</li>
              <li>• Conversation history maintained during session</li>
              <li>• Responsive design</li>
            </ul>
          </div>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Setup Required:</h3>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              To test the chatbot, make sure you have:
            </p>
            <ol className="text-sm text-yellow-700 dark:text-yellow-300 mt-2 space-y-1">
              <li>1. Set OPENAI_API_KEY in your API .env file</li>
              <li>2. Start both API and web servers</li>
              <li>3. Click the chat icon to start chatting!</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
