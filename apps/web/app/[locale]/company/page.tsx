"use client";

import { useTranslations } from "next-intl";
import { LandingNavbar } from "@/components/organisms";

export default function CompanyPage() {
  const t = useTranslations("common");

  return (
    <div className="min-h-screen bg-gray-50">
      <LandingNavbar />
      
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            About CheckU
          </h1>
          <p className="text-xl text-gray-600">
            Transforming the future of recruitment and talent acquisition
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg border p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Our Mission</h2>
          <p className="text-gray-600 text-lg leading-relaxed mb-6">
            At CheckU, we believe that finding the right talent should be efficient, fair, and transparent. 
            Our platform leverages cutting-edge technology to streamline the hiring process, making it easier 
            for employers to find qualified candidates and for job seekers to discover opportunities that match their skills.
          </p>
          
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">Our Vision</h2>
          <p className="text-gray-600 text-lg leading-relaxed">
            To create a world where every person can find meaningful work and every organization can build 
            exceptional teams through intelligent, data-driven recruitment solutions.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white p-6 rounded-xl shadow-lg border">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">For Employers</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• Advanced candidate screening</li>
              <li>• AI-powered resume analysis</li>
              <li>• Streamlined interview process</li>
              <li>• Comprehensive reporting</li>
            </ul>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg border">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">For Job Seekers</h3>
            <ul className="text-gray-600 space-y-2">
              <li>• Smart job matching</li>
              <li>• Resume optimization</li>
              <li>• Career guidance</li>
              <li>• Application tracking</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
