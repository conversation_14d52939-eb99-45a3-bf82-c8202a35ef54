"use client";

import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { LoginButtons } from "@/components/organisms/Auth/LoginButtons";
import { LocaleSwitcher } from "@/components/molecules/LocaleSwitcher";

export default function HomePage() {
  const t = useTranslations("common");
  const { status } = useSession();

  if (!t) {
    return <div>Loading translations...</div>;
  }

  // If user is authenticated, show the main dashboard
  if (status === "authenticated") {
    return (
      <main className="min-h-screen p-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-2xl font-bold">
              {t("welcomeBack") || "Welcome back to CheckU!"}
            </h1>
            <div className="flex items-center space-x-4">
              <LocaleSwitcher />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <p className="text-lg">You&apos;re successfully signed in!</p>
              <p>Current language: {t("language")}</p>

              <div className="mt-8 space-y-4">
                <h2 className="text-xl font-semibold">Quick Actions</h2>
                <div className="space-y-2">
                  <a
                    href="/resume-upload"
                    className="block p-3 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 transition-colors"
                  >
                    <div className="font-medium text-blue-900">
                      Upload Resume
                    </div>
                    <div className="text-sm text-blue-700">
                      Upload and extract data from your resume
                    </div>
                  </a>
                  <div className="text-sm text-gray-500 space-y-1">
                    <div>• {t("home")}</div>
                    <div>• {t("about")}</div>
                    <div>• {t("contact")}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <LoginButtons />
            </div>
          </div>
        </div>
      </main>
    );
  }

  // If user is not authenticated, show the beautiful landing page
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with language switcher */}
      <div className="absolute top-4 right-4">
        <LocaleSwitcher />
      </div>

      {/* Main landing content */}
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-lg border">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-16 h-16 bg-blue-600 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold text-white">C</span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Welcome to CheckU
              </h1>
              <p className="text-gray-600 text-lg">{t("welcome")}</p>
            </div>

            <LoginButtons />
          </div>
        </div>
      </div>
    </div>
  );
}
