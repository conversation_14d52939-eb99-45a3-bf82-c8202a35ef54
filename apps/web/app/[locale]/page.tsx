"use client";

import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { ResumeProcessor } from "@/components/organisms/ResumeProcessor";
import { LoginButtons } from "@/components/organisms/Auth/LoginButtons";
import { LocaleSwitcher } from "@/components/molecules/LocaleSwitcher";
import { LandingNavbar } from "@/components/organisms";

export default function HomePage() {
  const t = useTranslations("common");
  const { status } = useSession();

  if (!t) {
    return <div>Loading translations...</div>;
  }

  // If user is authenticated, show the main dashboard
  if (status === "authenticated") {
    return (
      <main className="min-h-screen p-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-2xl font-bold">
              {t("welcomeBack") || "Welcome back to CheckU!"}
            </h1>
            <div className="flex items-center space-x-4">
              <LocaleSwitcher />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <p className="text-lg">You&apos;re successfully signed in!</p>
              <p>Current language: {t("language")}</p>

              <div className="mt-8 space-y-4">
                <h2 className="text-xl font-semibold">Quick Actions</h2>
                <div className="space-y-2">
                  <a
                    href="/resume-upload"
                    className="block p-3 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 transition-colors"
                  >
                    <div className="font-medium text-blue-900">
                      Upload Resume
                    </div>
                    <div className="text-sm text-blue-700">
                      Upload and extract data from your resume
                    </div>
                  </a>
                  <div className="text-sm text-gray-500 space-y-1">
                    <div>• {t("home")}</div>
                    <div>• {t("about")}</div>
                    <div>• {t("contact")}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <LoginButtons />
            </div>
          </div>
        </div>
      </main>
    );
  }

  // If user is not authenticated, show the resume upload landing page
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Landing Navbar */}
      <LandingNavbar />

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Unlock Your Career{" "}
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
              Potential
            </span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Upload your resume and let our AI-powered system extract and
            optimize your professional information for better job opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#upload-section"
              className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors duration-200 shadow-lg"
            >
              Get Started Now
            </a>
            <button
              onClick={() =>
                document
                  .getElementById("features-section")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              Learn More
            </button>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div id="features-section" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose CheckU?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our advanced AI technology makes resume processing simple, fast,
              and accurate.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Lightning Fast
              </h3>
              <p className="text-gray-600">
                Extract data from your resume in seconds with our AI-powered
                technology.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Highly Accurate
              </h3>
              <p className="text-gray-600">
                Our advanced algorithms ensure precise extraction of your
                professional information.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-8 h-8 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Secure & Private
              </h3>
              <p className="text-gray-600">
                Your data is encrypted and secure. We respect your privacy and
                confidentiality.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Resume Upload Section */}
      <div id="upload-section" className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Upload Your Resume
            </h2>
            <p className="text-xl text-gray-600">
              Get started by uploading your resume. Our AI will extract and
              organize your information automatically.
            </p>
          </div>

          <ResumeProcessor
            onSuccess={(data) => {
              console.log("Resume processed successfully:", data);
              // You can add additional success handling here
            }}
          />
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Transform Your Career?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Join thousands of professionals who have already optimized their
            resumes with CheckU.
          </p>
          <a
            href="#upload-section"
            className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors duration-200 shadow-lg inline-block"
          >
            Upload Your Resume Now
          </a>
        </div>
      </div>
    </div>
  );
}
