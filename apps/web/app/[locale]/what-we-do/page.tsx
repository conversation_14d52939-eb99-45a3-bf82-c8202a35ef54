"use client";

import { useTranslations } from "next-intl";
import { LandingNavbar } from "@/components/organisms";

export default function WhatWeDoPage() {
  const t = useTranslations("common");

  return (
    <div className="min-h-screen bg-gray-50">
      <LandingNavbar />
      
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            What We Do
          </h1>
          <p className="text-xl text-gray-600">
            Discover how CheckU revolutionizes the hiring process
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-xl shadow-lg border">
            <div className="w-12 h-12 bg-blue-600 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-white font-bold">1</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Resume Processing
            </h3>
            <p className="text-gray-600">
              Advanced AI-powered resume parsing and analysis to extract key information efficiently.
            </p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg border">
            <div className="w-12 h-12 bg-green-600 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-white font-bold">2</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Candidate Matching
            </h3>
            <p className="text-gray-600">
              Smart matching algorithms to connect the right candidates with the right opportunities.
            </p>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg border">
            <div className="w-12 h-12 bg-purple-600 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-white font-bold">3</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Hiring Management
            </h3>
            <p className="text-gray-600">
              Comprehensive tools for managing the entire hiring process from start to finish.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
