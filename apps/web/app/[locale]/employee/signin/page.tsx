"use client";

import { useTranslations } from "next-intl";
import { LandingNavbar } from "@/components/organisms";
import { LoginButtons } from "@/components/organisms/Auth/LoginButtons";

export default function EmployeeSignInPage() {
  const t = useTranslations("common");

  return (
    <div className="min-h-screen bg-gray-50">
      <LandingNavbar />
      
      <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] px-4">
        <div className="max-w-md w-full p-8 bg-white rounded-xl shadow-lg border">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-16 h-16 bg-green-600 rounded-xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold text-white">E</span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Employee Sign In
              </h1>
              <p className="text-gray-600 text-lg">
                Sign in to access your employee dashboard
              </p>
            </div>

            <LoginButtons />
          </div>
        </div>
      </div>
    </div>
  );
}
