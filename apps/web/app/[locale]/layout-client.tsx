'use client';

import { NextIntlClientProvider } from 'next-intl';
import { ReactNode } from 'react';

type Messages = {
  welcome: string;
  language: string;
  english: string;
  thai: string;
  home: string;
  about: string;
  contact: string;
};

type Props = {
  locale: string;
  messages: {
    common: Messages;
  };
  children: ReactNode;
};

export default function LocaleLayoutClient({ 
  locale, 
  messages, 
  children 
}: Props) {
  return (
    <NextIntlClientProvider 
      locale={locale} 
      messages={messages}
      timeZone="Asia/Bangkok"
      now={new Date()}
    >
      {children}
    </NextIntlClientProvider>
  );
}
