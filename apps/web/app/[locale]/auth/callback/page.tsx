"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { ProfileCompletionModal } from "@/components/organisms/Auth/ProfileCompletionModal";

function AuthCallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [message, setMessage] = useState("");
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [userData, setUserData] = useState<any>(null);

  useEffect(() => {
    console.log("AuthCallback useEffect triggered");
    const success = searchParams.get("success");
    const error = searchParams.get("error");
    const user = searchParams.get("user");

    console.log("URL params:", {
      success,
      error,
      user: user ? "present" : "missing",
    });

    if (success === "true") {
      if (user) {
        try {
          console.log("Parsing user data...");
          const parsedUserData = JSON.parse(decodeURIComponent(user));
          console.log("Parsed user data:", parsedUserData);
          setUserData(parsedUserData);
          localStorage.setItem("user", JSON.stringify(parsedUserData));

          // Check if user is new and needs to complete profile
          if (parsedUserData.isNewUser) {
            console.log("New user detected, showing profile modal");
            setStatus("success");
            setMessage("Welcome! Please complete your profile.");
            setShowProfileModal(true);
            return; // Don't redirect yet
          } else {
            console.log("Existing user, redirecting to home");
          }
        } catch (e) {
          console.error("Failed to parse user data:", e);
        }
      }

      setStatus("success");
      setMessage("Successfully signed in!");

      setTimeout(() => {
        router.push("/");
      }, 2000);
    } else if (success === "false") {
      setStatus("error");
      setMessage(error ? decodeURIComponent(error) : "Authentication failed");
    } else {
      console.log("No success parameter found, checking URL manually");
      // Fallback: check URL manually if searchParams doesn't work
      const url = window.location.href;
      console.log("Current URL:", url);

      if (url.includes("success=true")) {
        console.log("Found success=true in URL, processing manually");
        // Manual parsing as fallback
        const urlParams = new URLSearchParams(window.location.search);
        const manualSuccess = urlParams.get("success");
        const manualUser = urlParams.get("user");
        console.log("Manual params:", {
          manualSuccess,
          manualUser: manualUser ? "present" : "missing",
        });

        if (manualUser) {
          try {
            const parsedUserData = JSON.parse(decodeURIComponent(manualUser));
            console.log("Manual parsed user data:", parsedUserData);
            setUserData(parsedUserData);
            localStorage.setItem("user", JSON.stringify(parsedUserData));

            if (parsedUserData.isNewUser) {
              console.log("Manual: New user detected, showing profile modal");
              setStatus("success");
              setMessage("Welcome! Please complete your profile.");
              setShowProfileModal(true);
              return;
            }
          } catch (e) {
            console.error("Manual parsing failed:", e);
          }
        }

        setStatus("success");
        setMessage("Successfully signed in!");
        setTimeout(() => {
          router.push("/");
        }, 2000);
      }
    }
  }, [searchParams, router]);

  const handleProfileModalClose = () => {
    setShowProfileModal(false);
    // Redirect to home after profile completion
    setTimeout(() => {
      router.push("/");
    }, 1000);
  };

  return (
    <>
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            {status === "loading" && (
              <>
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <h2 className="mt-6 text-xl font-semibold text-gray-900">
                  Processing authentication...
                </h2>
              </>
            )}

            {status === "success" && (
              <>
                <div className="rounded-full h-12 w-12 bg-green-100 mx-auto flex items-center justify-center">
                  <svg
                    className="h-6 w-6 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    ></path>
                  </svg>
                </div>
                <h2 className="mt-6 text-xl font-semibold text-green-900">
                  {message}
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  Redirecting you to the home page...
                </p>
              </>
            )}

            {status === "error" && (
              <>
                <div className="rounded-full h-12 w-12 bg-red-100 mx-auto flex items-center justify-center">
                  <svg
                    className="h-6 w-6 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    ></path>
                  </svg>
                </div>
                <h2 className="mt-6 text-xl font-semibold text-red-900">
                  Authentication Failed
                </h2>
                <p className="mt-2 text-sm text-gray-600">{message}</p>
                <button
                  onClick={() => router.push("/auth/signin")}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  Try Again
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Profile Completion Modal */}
      <ProfileCompletionModal
        isOpen={showProfileModal}
        onClose={handleProfileModalClose}
        initialData={{
          email: userData?.email,
          name: userData?.name,
        }}
      />
    </>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      }
    >
      <AuthCallbackContent />
    </Suspense>
  );
}
