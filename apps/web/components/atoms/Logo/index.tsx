"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "white";
  href?: string;
}

const sizeClasses = {
  sm: "h-8 w-8 text-lg",
  md: "h-12 w-12 text-xl",
  lg: "h-16 w-16 text-2xl",
};

const variantClasses = {
  default: "bg-blue-600 text-white",
  white: "bg-white text-blue-600 border border-blue-600",
};

export function Logo({ 
  className, 
  size = "md", 
  variant = "default",
  href = "/" 
}: LogoProps) {
  const logoElement = (
    <div className={cn(
      "rounded-xl flex items-center justify-center font-bold transition-all duration-200 hover:scale-105",
      sizeClasses[size],
      variantClasses[variant],
      className
    )}>
      <span>C</span>
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="inline-block">
        {logoElement}
      </Link>
    );
  }

  return logoElement;
}

export default Logo;
