# Atomic Design Component Structure

This project uses atomic design principles to organize components in a hierarchical structure. This approach helps create a consistent, maintainable, and scalable component library.

## Structure Overview

```
/components/
├── atoms/             # Smallest building blocks
├── molecules/         # Groups of atoms functioning together
├── organisms/         # Complex UI components
└── templates/         # Page layouts
```

## Categories

### Atoms
The smallest, indivisible UI components that serve as building blocks for all other components.

- `Button`: Basic button component
- `Code`: Code display component

### Molecules
Simple combinations of atoms that function together as a unit.

- `Card`: Card component for displaying content with a title and link

### Organisms
Complex UI components composed of molecules and atoms that form distinct sections of the interface.

- `Auth/`: Authentication-related components
  - `GoogleSignInButton`: Button for Google authentication
  - `LinkedInSignInButton`: Button for LinkedIn authentication
  - `LoginButtons`: Component that displays authentication options
  - `UserProfile`: Component that displays user information when authenticated

### Templates
Page layouts that arrange organisms, molecules, and atoms into a complete page structure.

- `Auth/AuthProvider`: Provides authentication context to the application

## Usage

Components can be imported directly from their respective folders:

```tsx
import { Button } from '@/components/atoms/Button';
```

Or using the barrel exports:

```tsx
import { Button } from '@/components/atoms';
```

For convenience, all components are also exported from the root components directory:

```tsx
import { Button, Card, GoogleSignInButton } from '@/components';
```

## Best Practices

1. **Component Placement**: Place components in the appropriate category based on their complexity and purpose
2. **Composition**: Build complex components by composing simpler ones
3. **Props**: Keep props interfaces consistent and well-documented
4. **Reusability**: Prioritize reusability, especially for atoms and molecules
5. **Naming**: Use clear, descriptive names that indicate the component's purpose
