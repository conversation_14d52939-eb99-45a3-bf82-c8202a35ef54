"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { FileInput } from "@/components/atoms/FileInput";
import { Input } from "@/components/atoms/Input";
import { Button } from "@/components/atoms/Button";
import { LoadingSpinner } from "@/components/atoms/LoadingSpinner";
import { FormField } from "@/components/molecules/FormField";
import { cn } from "@/lib/utils";

const resumeFormSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  location: z.string().min(1, "Location is required"),
  jobTitle: z.string().min(1, "Job title is required"),
  experience: z.string().min(1, "Experience is required"),
  skills: z.string().min(1, "Skills are required"),
  education: z.string().min(1, "Education is required"),
  summary: z.string().optional(),
});

type ResumeFormData = z.infer<typeof resumeFormSchema>;

interface ResumeUploadFormProps {
  onSubmit: (data: ResumeFormData & { file: File }) => void;
  isLoading?: boolean;
  className?: string;
}

export function ResumeUploadForm({
  onSubmit,
  isLoading = false,
  className
}: ResumeUploadFormProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isExtracting, setIsExtracting] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset
  } = useForm<ResumeFormData>({
    resolver: zodResolver(resumeFormSchema),
  });

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    setIsExtracting(true);

    try {
      // Call API to extract data from resume
      const formData = new FormData();
      formData.append("resume", file);

      const response = await fetch("/api/resume/extract", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const extractedData = await response.json();
        
        // Populate form fields with extracted data
        Object.entries(extractedData).forEach(([key, value]) => {
          if (value && typeof value === "string") {
            setValue(key as keyof ResumeFormData, value);
          }
        });
      }
    } catch (error) {
      console.error("Error extracting resume data:", error);
    } finally {
      setIsExtracting(false);
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    reset();
  };

  const onFormSubmit = (data: ResumeFormData) => {
    if (!selectedFile) return;
    onSubmit({ ...data, file: selectedFile });
  };

  return (
    <form
      onSubmit={handleSubmit(onFormSubmit)}
      className={cn("space-y-6", className)}
    >
      <FormField
        label="Upload Resume"
        required
        error={!selectedFile ? "Please upload a resume file" : undefined}
      >
        <FileInput
          onFileSelect={handleFileSelect}
          onFileRemove={handleFileRemove}
          selectedFile={selectedFile}
          disabled={isExtracting || isLoading}
        />
        {isExtracting && (
          <div className="flex items-center space-x-2 mt-2">
            <LoadingSpinner size="sm" />
            <span className="text-sm text-gray-600">
              Extracting data from resume...
            </span>
          </div>
        )}
      </FormField>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          label="Full Name"
          htmlFor="fullName"
          required
          error={errors.fullName?.message}
        >
          <Input
            id="fullName"
            {...register("fullName")}
            placeholder="Enter your full name"
            disabled={isExtracting || isLoading}
          />
        </FormField>

        <FormField
          label="Email"
          htmlFor="email"
          required
          error={errors.email?.message}
        >
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="Enter your email"
            disabled={isExtracting || isLoading}
          />
        </FormField>

        <FormField
          label="Phone"
          htmlFor="phone"
          required
          error={errors.phone?.message}
        >
          <Input
            id="phone"
            {...register("phone")}
            placeholder="Enter your phone number"
            disabled={isExtracting || isLoading}
          />
        </FormField>

        <FormField
          label="Location"
          htmlFor="location"
          required
          error={errors.location?.message}
        >
          <Input
            id="location"
            {...register("location")}
            placeholder="Enter your location"
            disabled={isExtracting || isLoading}
          />
        </FormField>

        <FormField
          label="Job Title"
          htmlFor="jobTitle"
          required
          error={errors.jobTitle?.message}
        >
          <Input
            id="jobTitle"
            {...register("jobTitle")}
            placeholder="Enter your current/desired job title"
            disabled={isExtracting || isLoading}
          />
        </FormField>

        <FormField
          label="Years of Experience"
          htmlFor="experience"
          required
          error={errors.experience?.message}
        >
          <Input
            id="experience"
            {...register("experience")}
            placeholder="e.g., 5 years"
            disabled={isExtracting || isLoading}
          />
        </FormField>
      </div>

      <FormField
        label="Skills"
        htmlFor="skills"
        required
        error={errors.skills?.message}
      >
        <Input
          id="skills"
          {...register("skills")}
          placeholder="Enter your key skills (comma separated)"
          disabled={isExtracting || isLoading}
        />
      </FormField>

      <FormField
        label="Education"
        htmlFor="education"
        required
        error={errors.education?.message}
      >
        <Input
          id="education"
          {...register("education")}
          placeholder="Enter your education background"
          disabled={isExtracting || isLoading}
        />
      </FormField>

      <FormField
        label="Professional Summary"
        htmlFor="summary"
        error={errors.summary?.message}
      >
        <textarea
          id="summary"
          {...register("summary")}
          placeholder="Enter a brief professional summary (optional)"
          disabled={isExtracting || isLoading}
          rows={4}
          className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none"
        />
      </FormField>

      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={!selectedFile || isExtracting || isLoading}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <LoadingSpinner size="sm" />
              <span>Submitting...</span>
            </div>
          ) : (
            "Submit Application"
          )}
        </Button>
      </div>
    </form>
  );
}

export default ResumeUploadForm;
