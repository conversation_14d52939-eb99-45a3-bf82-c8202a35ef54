"use client";

import { ArrowUpRight } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Card as Shadcn<PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "./components";
import { Button } from "@/components/atoms/Button";

interface CardProps {
  className?: string;
  title: string;
  children: React.ReactNode;
  href: string;
  description?: string;
  footerContent?: React.ReactNode;
}

export function Card({
  className,
  title,
  children,
  href,
  description,
  footerContent,
}: Readonly<CardProps>) {
  return (
    <ShadcnCard className={cn("overflow-hidden transition-all duration-200 hover:shadow-md", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {typeof children === "string" ? (
          <p className="text-muted-foreground">{children}</p>
        ) : (
          children
        )}
      </CardContent>
      <CardFooter className="flex justify-between items-center">
        {footerContent}
        <Button 
          variant="ghost" 
          size="sm" 
          className="gap-1 text-primary hover:text-primary/80"
          onClick={() => window.open(href, "_blank", "noopener,noreferrer")}
        >
          View
          <ArrowUpRight className="h-4 w-4" />
        </Button>
      </CardFooter>
    </ShadcnCard>
  );
}

export default Card;