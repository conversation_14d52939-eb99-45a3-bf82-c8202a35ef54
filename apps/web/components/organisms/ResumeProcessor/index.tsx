"use client";

import React, { useState } from "react";
import { ResumeUploadForm } from "@/components/molecules/ResumeUploadForm";
import { Card } from "@/components/molecules/Card";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface ResumeData {
  fullName: string;
  email: string;
  phone: string;
  location: string;
  jobTitle: string;
  experience: string;
  skills: string;
  education: string;
  summary?: string;
  file: File;
}

interface ResumeProcessorProps {
  onSuccess?: (data: ResumeData) => void;
  className?: string;
}

export function ResumeProcessor({ onSuccess, className }: ResumeProcessorProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: "success" | "error" | null;
    message: string;
  }>({ type: null, message: "" });

  const handleSubmit = async (data: ResumeData) => {
    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: "" });

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append("resume", data.file);
      formData.append("fullName", data.fullName);
      formData.append("email", data.email);
      formData.append("phone", data.phone);
      formData.append("location", data.location);
      formData.append("jobTitle", data.jobTitle);
      formData.append("experience", data.experience);
      formData.append("skills", data.skills);
      formData.append("education", data.education);
      if (data.summary) {
        formData.append("summary", data.summary);
      }

      const response = await fetch("/api/resume/submit", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setSubmitStatus({
          type: "success",
          message: "Resume submitted successfully! We'll be in touch soon.",
        });
        onSuccess?.(data);
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to submit resume");
      }
    } catch (error) {
      console.error("Error submitting resume:", error);
      setSubmitStatus({
        type: "error",
        message: error instanceof Error ? error.message : "Failed to submit resume. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn("max-w-4xl mx-auto", className)}>
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Upload Your Resume
        </h1>
        <p className="text-lg text-gray-600">
          Upload your resume and we'll automatically extract your information to speed up the application process.
        </p>
      </div>

      {submitStatus.type && (
        <div
          className={cn(
            "mb-6 p-4 rounded-lg flex items-center space-x-3",
            submitStatus.type === "success"
              ? "bg-green-50 border border-green-200"
              : "bg-red-50 border border-red-200"
          )}
        >
          {submitStatus.type === "success" ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <AlertCircle className="h-5 w-5 text-red-600" />
          )}
          <p
            className={cn(
              "text-sm font-medium",
              submitStatus.type === "success" ? "text-green-800" : "text-red-800"
            )}
          >
            {submitStatus.message}
          </p>
        </div>
      )}

      <Card className="p-8">
        <ResumeUploadForm
          onSubmit={handleSubmit}
          isLoading={isSubmitting}
        />
      </Card>

      <div className="mt-8 text-center text-sm text-gray-500">
        <p>
          Your information is secure and will only be used for application purposes.
        </p>
      </div>
    </div>
  );
}

export default ResumeProcessor;
