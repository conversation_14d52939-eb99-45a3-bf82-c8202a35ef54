"use client";

import { useState, useEffect } from "react";
import { Search, Plus, Edit, Trash2, Eye, Users, Globe } from "lucide-react";
import { adminApi, Company } from "@/lib/api/admin";

interface CompanyWithEmployeeCount extends Company {
  employeeCount: number;
}

export function CompanyManagement() {
  const [companies, setCompanies] = useState<CompanyWithEmployeeCount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const [companyData, employeeData] = await Promise.all([
          adminApi.getCompanies(),
          adminApi.getEmployees(),
        ]);

        // Add employee count to each company
        const companiesWithCount = companyData.map((company) => ({
          ...company,
          employeeCount: employeeData.filter(
            (emp) => emp.companyId === company.id
          ).length,
        }));

        setCompanies(companiesWithCount);
      } catch (err) {
        console.error("Failed to fetch companies:", err);
        setError("Failed to load companies");
        // Fallback to mock data for demo
        setCompanies([
          {
            id: "1",
            name: "Tech Corp",
            description: "Leading technology company",
            website: "https://techcorp.com",
            industry: "Technology",
            size: "large",
            employeeCount: 45,
            createdAt: "2024-01-15T10:00:00Z",
            updatedAt: "2024-01-15T10:00:00Z",
          },
          {
            id: "2",
            name: "Startup Inc",
            description: "Innovative startup company",
            website: "https://startup.com",
            industry: "Software",
            size: "startup",
            employeeCount: 12,
            createdAt: "2024-02-01T14:30:00Z",
            updatedAt: "2024-02-01T14:30:00Z",
          },
          {
            id: "3",
            name: "Enterprise Solutions",
            description: "Enterprise software solutions",
            industry: "Enterprise Software",
            size: "enterprise",
            employeeCount: 156,
            createdAt: "2024-01-10T09:15:00Z",
            updatedAt: "2024-01-10T09:15:00Z",
          },
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  const filteredCompanies = companies.filter(
    (company) =>
      company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.industry?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteCompany = async (companyId: string) => {
    if (
      confirm(
        "Are you sure you want to delete this company? This will also remove all associated employees."
      )
    ) {
      try {
        await adminApi.deleteCompany(companyId);
        setCompanies(companies.filter((company) => company.id !== companyId));
      } catch (err) {
        console.error("Failed to delete company:", err);
        alert("Failed to delete company. Please try again.");
      }
    }
  };

  const getSizeColor = (size?: string) => {
    switch (size) {
      case "startup":
        return "bg-purple-100 text-purple-800";
      case "small":
        return "bg-blue-100 text-blue-800";
      case "medium":
        return "bg-green-100 text-green-800";
      case "large":
        return "bg-orange-100 text-orange-800";
      case "enterprise":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Add Company</span>
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Company
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Industry
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Employees
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredCompanies.map((company) => (
              <tr key={company.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900 flex items-center">
                      {company.name}
                      {company.website && (
                        <a
                          href={company.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 hover:text-blue-800"
                        >
                          <Globe className="h-4 w-4" />
                        </a>
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      {company.description || "No description"}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-900">
                    {company.industry || "N/A"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSizeColor(company.size)}`}
                  >
                    {company.size || "unknown"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900">
                    <Users className="h-4 w-4 text-gray-400 mr-1" />
                    {company.employeeCount}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(company.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-green-600 hover:text-green-900">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteCompany(company.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredCompanies.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No companies found</p>
        </div>
      )}
    </div>
  );
}
