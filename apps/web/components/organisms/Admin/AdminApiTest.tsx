"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, XCircle, Loader2 } from "lucide-react";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";

export function AdminApiTest() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    data?: any;
  } | null>(null);

  const testApiConnection = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch(`${API_BASE_URL}/admin/public/users`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setResult({
          success: true,
          message: "API connection successful!",
          data: { message: "Users endpoint working", userCount: data.length },
        });
      } else {
        setResult({
          success: false,
          message: `API Error: ${response.status} - ${response.statusText}`,
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: `Connection Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testStatsEndpoint = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch(`${API_BASE_URL}/admin/public/stats`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setResult({
          success: true,
          message: "Stats endpoint working!",
          data,
        });
      } else {
        setResult({
          success: false,
          message: `API Error: ${response.status} - ${response.statusText}`,
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: `Connection Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        API Connection Test
      </h3>

      <div className="space-y-4">
        <div className="flex space-x-4">
          <button
            onClick={testApiConnection}
            disabled={isLoading}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <span>Test Basic Connection</span>
            )}
          </button>

          <button
            onClick={testStatsEndpoint}
            disabled={isLoading}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <span>Test Stats Endpoint</span>
            )}
          </button>
        </div>

        {result && (
          <div
            className={`p-4 rounded-md ${result.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"}`}
          >
            <div className="flex items-center space-x-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <span
                className={`font-medium ${result.success ? "text-green-800" : "text-red-800"}`}
              >
                {result.message}
              </span>
            </div>

            {result.data && (
              <div className="mt-3">
                <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p>
            <strong>API Base URL:</strong> {API_BASE_URL}
          </p>
          <p>
            <strong>Test Endpoints:</strong>
          </p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>GET /admin/public/users - Users endpoint test</li>
            <li>GET /admin/public/stats - Stats endpoint test</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
