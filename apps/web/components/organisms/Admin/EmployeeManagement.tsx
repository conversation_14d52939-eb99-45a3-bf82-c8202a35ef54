"use client";

import { useState, useEffect } from "react";
import { Search, Plus, Edit, Trash2, Eye, Building2 } from "lucide-react";
import { adminApi, Employee, User, Company } from "@/lib/api/admin";

interface EmployeeWithDetails extends Employee {
  user: User;
  company: Company;
}

export function EmployeeManagement() {
  const [employees, setEmployees] = useState<EmployeeWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const [employeeData, userData, companyData] = await Promise.all([
          adminApi.getEmployees(),
          adminApi.getUsers(),
          adminApi.getCompanies(),
        ]);

        // Combine employee data with user and company details
        const employeesWithDetails = employeeData.map((employee) => {
          const user = userData.find((u) => u.id === employee.userId);
          const company = companyData.find((c) => c.id === employee.companyId);

          return {
            ...employee,
            user: user || {
              id: employee.userId,
              email: "<EMAIL>",
              name: "Unknown User",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            company: company || {
              id: employee.companyId,
              name: "Unknown Company",
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          };
        });

        setEmployees(employeesWithDetails);
      } catch (err) {
        console.error("Failed to fetch employees:", err);
        setError("Failed to load employees");
        // Fallback to mock data for demo
        setEmployees([
          {
            id: "1",
            userId: "user1",
            companyId: "comp1",
            position: "Software Engineer",
            department: "Engineering",
            status: "active",
            startDate: "2024-01-15",
            createdAt: "2024-01-15T10:00:00Z",
            updatedAt: "2024-01-15T10:00:00Z",
            user: {
              id: "user1",
              name: "John Doe",
              email: "<EMAIL>",
              picture: "https://lh3.googleusercontent.com/a/default-user",
              createdAt: "2024-01-15T10:00:00Z",
              updatedAt: "2024-01-15T10:00:00Z",
            },
            company: {
              id: "comp1",
              name: "Tech Corp",
              createdAt: "2024-01-15T10:00:00Z",
              updatedAt: "2024-01-15T10:00:00Z",
            },
          },
          {
            id: "2",
            userId: "user2",
            companyId: "comp1",
            position: "Product Manager",
            department: "Product",
            status: "active",
            startDate: "2024-02-01",
            createdAt: "2024-02-01T10:00:00Z",
            updatedAt: "2024-02-01T10:00:00Z",
            user: {
              id: "user2",
              name: "Jane Smith",
              email: "<EMAIL>",
              createdAt: "2024-02-01T10:00:00Z",
              updatedAt: "2024-02-01T10:00:00Z",
            },
            company: {
              id: "comp1",
              name: "Tech Corp",
              createdAt: "2024-01-15T10:00:00Z",
              updatedAt: "2024-01-15T10:00:00Z",
            },
          },
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployees();
  }, []);

  const filteredEmployees = employees.filter(
    (employee) =>
      employee.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.position?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteEmployee = async (employeeId: string) => {
    if (confirm("Are you sure you want to delete this employee record?")) {
      try {
        await adminApi.deleteEmployee(employeeId);
        setEmployees(employees.filter((emp) => emp.id !== employeeId));
      } catch (err) {
        console.error("Failed to delete employee:", err);
        alert("Failed to delete employee. Please try again.");
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-yellow-100 text-yellow-800";
      case "terminated":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-12 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Add Employee</span>
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Employee
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Company
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Position
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Start Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredEmployees.map((employee) => (
              <tr key={employee.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {employee.user.picture ? (
                      <img
                        src={employee.user.picture}
                        alt={employee.user.name || employee.user.email}
                        className="h-10 w-10 rounded-full"
                      />
                    ) : (
                      <div className="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700">
                          {(employee.user.name || employee.user.email)
                            .charAt(0)
                            .toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {employee.user.name || "No name"}
                      </div>
                      <div className="text-sm text-gray-500">
                        {employee.user.email}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-900">
                      {employee.company.name}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {employee.position || "N/A"}
                  </div>
                  <div className="text-sm text-gray-500">
                    {employee.department || "N/A"}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(employee.status)}`}
                  >
                    {employee.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {employee.startDate
                    ? new Date(employee.startDate).toLocaleDateString()
                    : "N/A"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-green-600 hover:text-green-900">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteEmployee(employee.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredEmployees.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No employees found</p>
        </div>
      )}
    </div>
  );
}
