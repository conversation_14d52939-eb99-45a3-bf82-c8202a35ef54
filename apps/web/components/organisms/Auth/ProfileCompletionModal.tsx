"use client";

import { useState } from "react";
import { Modal } from "../../atoms/Modal";
import { Button } from "../../atoms/Button";
import { Input } from "../../atoms/Input";
import { useTranslations } from "next-intl";
import { Calendar, AlertTriangle } from "lucide-react";

interface ProfileCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: {
    email?: string;
    name?: string;
  };
}

interface ProfileFormData {
  email: string;
  fullName: string;
  phoneNumber: string;
  birthDate: string;
  jobPosition: string;
  address: string;
}

export function ProfileCompletionModal({
  isOpen,
  onClose,
  initialData,
}: ProfileCompletionModalProps) {
  const t = useTranslations("common");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<ProfileFormData>>({});
  const [formData, setFormData] = useState<ProfileFormData>({
    email: initialData?.email || "",
    fullName: initialData?.name || "",
    phoneNumber: "",
    birthDate: "",
    jobPosition: "",
    address: "",
  });

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileFormData> = {};

    // Required field validations
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required";
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    }

    if (!formData.birthDate.trim()) {
      newErrors.birthDate = "Birth date is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Get user data from localStorage to get the user ID
      const userData = localStorage.getItem("user");
      if (!userData) {
        throw new Error("User data not found");
      }

      const user = JSON.parse(userData);
      const userId = user.id;

      // Call profile completion API
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/complete-profile/${userId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: formData.email,
            name: formData.fullName,
            phoneNumber: formData.phoneNumber,
            birthDate: formData.birthDate,
            jobPosition: formData.jobPosition,
            address: formData.address,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to complete profile");
      }

      const result = await response.json();
      console.log("Profile completion result:", result);

      // Update localStorage with updated user data
      localStorage.setItem("user", JSON.stringify(result.user));

      // Show success message
      alert(
        "Profile completed successfully! A verification email has been sent to your email address."
      );

      onClose();
    } catch (error) {
      console.error("Profile completion error:", error);
      alert(`Failed to complete profile: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = () => {
    // Close this modal and potentially open sign in modal
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="max-w-lg w-full max-h-[calc(100vh-4rem)]"
      showCloseButton={true}
    >
      <div className="p-6 sm:p-8">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center px-3 py-1.5 bg-purple-100 text-purple-800 rounded-full text-xs font-medium mb-3">
            SIGN UP
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">TO DISCOVER</h2>
          <h2 className="text-2xl font-bold text-gray-900">YOURSELF!</h2>
        </div>

        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium">
                Your personal data directly impacts your personality results.
              </p>
              <p>
                Please ensure all information is accurate for the best analysis.
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email <span className="text-red-500">*</span>
            </label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              placeholder="Enter your email"
              className={`w-full bg-orange-50 border-orange-200 ${
                errors.email ? "border-red-500" : ""
              }`}
              disabled={!!initialData?.email}
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>

          {/* Full Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full name <span className="text-red-500">*</span>
            </label>
            <Input
              type="text"
              value={formData.fullName}
              onChange={(e) => handleInputChange("fullName", e.target.value)}
              placeholder="Enter your full name"
              className={`w-full bg-orange-50 border-orange-200 ${
                errors.fullName ? "border-red-500" : ""
              }`}
            />
            {errors.fullName && (
              <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone number <span className="text-red-500">*</span>
            </label>
            <Input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
              placeholder="Enter your phone number"
              className={`w-full bg-orange-50 border-orange-200 ${
                errors.phoneNumber ? "border-red-500" : ""
              }`}
            />
            {errors.phoneNumber && (
              <p className="text-red-500 text-xs mt-1">{errors.phoneNumber}</p>
            )}
          </div>

          {/* Birth Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Birth date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Input
                type="date"
                value={formData.birthDate}
                onChange={(e) => handleInputChange("birthDate", e.target.value)}
                className={`w-full bg-orange-50 border-orange-200 pr-10 ${
                  errors.birthDate ? "border-red-500" : ""
                }`}
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
            {errors.birthDate && (
              <p className="text-red-500 text-xs mt-1">{errors.birthDate}</p>
            )}
          </div>

          {/* Job Position */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Job position
            </label>
            <Input
              type="text"
              value={formData.jobPosition}
              onChange={(e) => handleInputChange("jobPosition", e.target.value)}
              placeholder="Enter job position"
              className="w-full bg-orange-50 border-orange-200"
            />
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address
            </label>
            <Input
              type="text"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              placeholder="Enter address"
              className="w-full bg-orange-50 border-orange-200"
            />
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-base font-medium"
            >
              {isLoading ? "Creating account..." : "Create an account"}
            </Button>
          </div>

          {/* Sign In Link */}
          <div className="text-center pt-2">
            <span className="text-sm text-gray-600">
              Already have an account?{" "}
              <button
                type="button"
                onClick={handleSignIn}
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Sign in
              </button>
            </span>
          </div>
        </form>
      </div>
    </Modal>
  );
}
