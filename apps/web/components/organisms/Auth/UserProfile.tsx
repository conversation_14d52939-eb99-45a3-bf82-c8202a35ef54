"use client";

import { useSession, signOut } from "next-auth/react";
import Image from "next/image";

export function UserProfile() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="animate-pulse flex items-center space-x-4">
          <div className="rounded-full bg-gray-200 h-12 w-12"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (status === "unauthenticated" || !session) {
    return null;
  }
  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <div className="flex items-center space-x-4">
        {session.user?.image ? (
          <div className="h-12 w-12 rounded-full overflow-hidden flex items-center justify-center">
            <Image
              src={session.user.image}
              alt={session.user?.name ?? "User"}
              className="h-full w-full object-cover"
              width={48}
              height={48}
              priority
            />
          </div>
        ) : (
          <div className="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center text-white text-xl font-bold">
            {session.user?.name?.charAt(0) ?? "U"}
          </div>
        )}

        <div className="flex-1">
          <h3 className="font-medium text-gray-900">
            {session.user?.name ?? "User"}
          </h3>
          <p className="text-sm text-gray-500">{session.user?.email ?? ""}</p>
        </div>

        <button
          onClick={() => signOut({ callbackUrl: "/" })}
          className="px-3 py-1 text-sm bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors"
        >
          Sign Out
        </button>
      </div>
    </div>
  );
}

export default UserProfile;