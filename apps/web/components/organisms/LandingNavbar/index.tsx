"use client";

import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { useTranslations } from "next-intl";
import { signOut } from "next-auth/react";
import { useCombinedAuth } from "@/hooks/use-combined-auth";
import Image from "next/image";
import { Logo } from "@/components/atoms";
import { LocaleSwitcher } from "@/components/molecules/LocaleSwitcher";
import { Button } from "@/components/atoms/Button";
import { SignInModal } from "@/components/organisms/Auth";
import { cn } from "@/lib/utils";
import { Menu, X, ChevronDown } from "lucide-react";

interface LandingNavbarProps {
  className?: string;
}

const navigationItems = [
  { key: "home", href: "/" },
  { key: "whatWeDo", href: "/what-we-do" },
  { key: "company", href: "/company" },
];

export function LandingNavbar({ className }: Readonly<LandingNavbarProps>) {
  const t = useTranslations("common");
  const { user, status } = useCombinedAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSignOut = () => {
    // Clear localStorage
    localStorage.removeItem("user");
    // Sign out from NextAuth
    signOut({ callbackUrl: "/" });
    setIsUserMenuOpen(false);
  };

  const renderUserProfile = () => {
    if (status === "loading") {
      return (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="hidden sm:block w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
      );
    }

    if (status === "authenticated" && user) {
      return (
        <div className="relative" ref={userMenuRef}>
          <button
            onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
            className="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            {user.image ? (
              <div className="w-8 h-8 rounded-full overflow-hidden">
                <Image
                  src={user.image}
                  alt={user.name || "User"}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                {user.name?.charAt(0) || "U"}
              </div>
            )}
            <span className="hidden sm:block text-sm font-medium text-gray-700 max-w-24 truncate">
              {user.name || "User"}
            </span>
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </button>

          {/* User dropdown menu */}
          {isUserMenuOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
              <div className="px-4 py-2 border-b border-gray-100">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.name || "User"}
                </p>
                <p className="text-xs text-gray-500 truncate">{user.email}</p>
              </div>
              <button
                onClick={handleSignOut}
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
              >
                Sign Out
              </button>
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  return (
    <>
      <nav
        className={cn(
          "bg-white/95 backdrop-blur-sm shadow-sm border-b border-gray-200 sticky top-0 z-50",
          className
        )}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side - Logo */}
            <div className="flex items-center">
              <Logo size="sm" href="/" />
              <Link href="/" className="ml-3 group">
                <span className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                  CheckU
                </span>
              </Link>
            </div>

            {/* Center - Navigation Menu */}
            <div className="hidden md:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.key}
                  href={item.href}
                  className="text-gray-600 hover:text-blue-600 px-4 py-2 text-sm font-medium transition-all duration-200 hover:bg-blue-50 rounded-lg relative group"
                >
                  {t(item.key) || item.key}
                  <span className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-3/4 transform -translate-x-1/2"></span>
                </Link>
              ))}
            </div>

            {/* Right side - Auth Links and Language Switcher */}
            <div className="flex items-center space-x-4">
              {/* Employer Site Button */}
              <div className="hidden sm:flex items-center">
                <Link href="/employer/signin">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-purple-600 text-purple-600 hover:bg-purple-50 transition-all duration-200"
                  >
                    Employer Site
                  </Button>
                </Link>
              </div>

              {/* User Profile or Sign In Button */}
              <div className="hidden sm:flex items-center">
                {status === "authenticated" && user ? (
                  renderUserProfile()
                ) : (
                  <Button
                    size="sm"
                    onClick={() => setIsSignInModalOpen(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    {t("signIn") || "Sign In"}
                  </Button>
                )}
              </div>

              {/* Language Switcher */}
              <LocaleSwitcher />

              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  {isMobileMenuOpen ? (
                    <X className="h-4 w-4" />
                  ) : (
                    <Menu className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden border-t border-gray-200 pt-4 pb-4 bg-white">
              <div className="space-y-1">
                {navigationItems.map((item) => (
                  <Link
                    key={item.key}
                    href={item.href}
                    className="block text-gray-600 hover:text-blue-600 hover:bg-blue-50 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-lg mx-2"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {t(item.key) || item.key}
                  </Link>
                ))}

                {/* Mobile auth section */}
                <div className="pt-4 px-2 space-y-3">
                  {/* Employer Site Button */}
                  <Link href="/employer/signin">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full border-purple-600 text-purple-600 hover:bg-purple-50 transition-all duration-200"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Employer Site
                    </Button>
                  </Link>

                  {/* User Profile or Sign In Button */}
                  {status === "authenticated" && user ? (
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center space-x-3">
                        {user.image ? (
                          <div className="w-10 h-10 rounded-full overflow-hidden">
                            <Image
                              src={user.image}
                              alt={user.name || "User"}
                              width={40}
                              height={40}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                            {user.name?.charAt(0) || "U"}
                          </div>
                        )}
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {user.name || "User"}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {user.email}
                          </p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          handleSignOut();
                          setIsMobileMenuOpen(false);
                        }}
                        className="w-full mt-3 text-red-600 border-red-200 hover:bg-red-50"
                      >
                        Sign Out
                      </Button>
                    </div>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => {
                        setIsSignInModalOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white transition-all duration-200 shadow-sm hover:shadow-md"
                    >
                      {t("signIn") || "Sign In"}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Sign In Modal - Outside nav to avoid z-index conflicts */}
      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
      />
    </>
  );
}

export default LandingNavbar;
