import { getRequestConfig } from "next-intl/server";
import { createRequestConfig, locales, pathnames, isValidLocale } from "./i18n/config";
import type { Locale, Pathnames } from "./i18n/config";

// Re-export all i18n configuration from the central config file
export { locales, pathnames, isValidLocale };
export type { Locale, Pathnames };

// Load messages for the requested locale using the shared config creator
export default getRequestConfig(async ({ locale }) => {
  const loadMessages = async (localeStr: string) => {
    return (await import(`./messages/${localeStr}/common.json`)).default;
  };
  
  return createRequestConfig(loadMessages)({ locale });
});
