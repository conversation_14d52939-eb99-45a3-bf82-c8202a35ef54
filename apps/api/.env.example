# Environment variables declared in this file are automatically made available to <PERSON>risma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/checku_db?schema=public"

# Application
PORT=4000
NODE_ENV=development

# Google OAuth
GOOGLE_CLIENT_ID=501059231718-22uc9r9b10hs6j41mekth3e523r3ffuv.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL="http://localhost:4000/auth/google/callback"

# LinkedIn OAuth
LINKEDIN_CLIENT_ID="86k93uz8r3qywx"
LINKEDIN_CLIENT_SECRET="WPL_AP1.vofH5ngFVg4hbw9Q.WBDqqQ=="
LINKEDIN_CALLBACK_URL="http://localhost:4000/auth/linkedin/callback"

# Frontend URL
FRONTEND_URL="http://localhost:3000"

# JWT (if you plan to add JWT authentication later)
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="7d"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"
