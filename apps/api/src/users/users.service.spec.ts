import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { DatabaseService } from '../database/database.service';

describe('UsersService', () => {
  let service: UsersService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    user: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a user', async () => {
      const userData = { email: '<EMAIL>', name: 'Test User' };
      const expectedUser = {
        id: '1',
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.user.create.mockResolvedValue(expectedUser);

      const result = await service.createUser(userData);

      expect(mockDatabaseService.user.create).toHaveBeenCalledWith({
        data: userData,
      });
      expect(result).toEqual(expectedUser);
    });
  });

  describe('findAllUsers', () => {
    it('should return an array of users', async () => {
      const expectedUsers = [
        {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User 1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: 'Test User 2',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockDatabaseService.user.findMany.mockResolvedValue(expectedUsers);

      const result = await service.findAllUsers();

      expect(mockDatabaseService.user.findMany).toHaveBeenCalled();
      expect(result).toEqual(expectedUsers);
    });
  });

  describe('findUserById', () => {
    it('should return a user by id', async () => {
      const userId = '1';
      const expectedUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.user.findUnique.mockResolvedValue(expectedUser);

      const result = await service.findUserById(userId);

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
      });
      expect(result).toEqual(expectedUser);
    });
  });
});
