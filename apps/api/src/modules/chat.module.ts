import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Chat<PERSON>ontroller } from '../presentation/controllers/chat.controller';
import { SendMessageUseCase } from '../application/use-cases/chat/send-message.use-case';

@Module({
  imports: [ConfigModule],
  controllers: [ChatController],
  providers: [SendMessageUseCase],
  exports: [SendMessageUseCase],
})
export class ChatModule {}
