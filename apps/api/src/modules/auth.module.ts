import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from '../presentation/controllers';
import { OAuthSignupUseCase, OAuthSigninUseCase } from '../application';
import { UserDomainService } from '../domain';
import { PrismaUserRepository } from '../infrastructure';
import { GoogleStrategy, LinkedInStrategy } from '../infrastructure/auth';
import { USER_REPOSITORY } from '../shared/tokens';

@Module({
  imports: [ConfigModule, PassportModule],
  controllers: [AuthController],
  providers: [
    // Use Cases
    OAuthSignupUseCase,
    OAuthSigninUseCase,

    // Domain Services
    UserDomainService,

    // Repository Implementation
    {
      provide: USER_REPOSITORY,
      useClass: PrismaUserRepository,
    },

    // Strategies
    GoogleStrategy,
    LinkedInStrategy,
  ],
  exports: [OAuthSignupUseCase, OAuthSigninUseCase],
})
export class AuthModule {}
