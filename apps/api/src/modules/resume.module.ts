import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ResumeController } from '../presentation/controllers/resume.controller';
import { ExtractResumeDataUseCase } from '../application/use-cases/resume/extract-resume-data.use-case';
import { SubmitResumeUseCase } from '../application/use-cases/resume/submit-resume.use-case';
import { ResumeParsingService } from '../application/services/resume-parsing.service';
import { DatabaseService } from '../database/database.service';

@Module({
  imports: [ConfigModule],
  controllers: [ResumeController],
  providers: [
    ExtractResumeDataUseCase,
    SubmitResumeUseCase,
    ResumeParsingService,
    DatabaseService,
  ],
  exports: [
    ExtractResumeDataUseCase,
    SubmitResumeUseCase,
    ResumeParsingService,
  ],
})
export class ResumeModule {}
