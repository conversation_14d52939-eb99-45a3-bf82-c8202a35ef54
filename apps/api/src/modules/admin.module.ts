import { Module } from '@nestjs/common';
import {
  Admin<PERSON>oleController,
  AdminUserController,
  AdminCompanyController,
  AdminEmployeeController,
  AdminTestController,
  AdminPublicController,
} from '../presentation/controllers/admin';
import {
  // Role use cases
  CreateRoleUseCase,
  FindRoleUseCase,
  UpdateRoleUseCase,
  DeleteRoleUseCase,
  // User use cases
  FindUserUseCase,
  UpdateUserUseCase,
  DeleteUserUseCase,
  // Company use cases
  CreateCompanyUseCase,
  FindCompanyUseCase,
  UpdateCompanyUseCase,
  DeleteCompanyUseCase,
  // Employee use cases
  CreateEmployeeUseCase,
  FindEmployeeUseCase,
  UpdateEmployeeUseCase,
  DeleteEmployeeUseCase,
} from '../application';
import { UserDomainService } from '../domain';
import {
  PrismaUserRepository,
  PrismaRoleRepository,
  PrismaCompanyRepository,
  PrismaEmployeeRepository,
} from '../infrastructure';
import {
  USER_REPOSITORY,
  ROLE_REPOSITORY,
  COMPANY_REPOSITORY,
  EMPLOYEE_REPOSITORY,
} from '../shared/tokens';

@Module({
  controllers: [
    AdminRoleController,
    AdminUserController,
    AdminCompanyController,
    AdminEmployeeController,
    AdminTestController,
    AdminPublicController,
  ],
  providers: [
    // Role Use Cases
    CreateRoleUseCase,
    FindRoleUseCase,
    UpdateRoleUseCase,
    DeleteRoleUseCase,

    // User Use Cases
    FindUserUseCase,
    UpdateUserUseCase,
    DeleteUserUseCase,

    // Company Use Cases
    CreateCompanyUseCase,
    FindCompanyUseCase,
    UpdateCompanyUseCase,
    DeleteCompanyUseCase,

    // Employee Use Cases
    CreateEmployeeUseCase,
    FindEmployeeUseCase,
    UpdateEmployeeUseCase,
    DeleteEmployeeUseCase,

    // Domain Services
    UserDomainService,

    // Repository Implementations
    {
      provide: USER_REPOSITORY,
      useClass: PrismaUserRepository,
    },
    {
      provide: ROLE_REPOSITORY,
      useClass: PrismaRoleRepository,
    },
    {
      provide: COMPANY_REPOSITORY,
      useClass: PrismaCompanyRepository,
    },
    {
      provide: EMPLOYEE_REPOSITORY,
      useClass: PrismaEmployeeRepository,
    },
  ],
  exports: [
    // Export use cases for potential use in other modules
    CreateRoleUseCase,
    FindRoleUseCase,
    UpdateRoleUseCase,
    DeleteRoleUseCase,
    CreateCompanyUseCase,
    FindCompanyUseCase,
    UpdateCompanyUseCase,
    DeleteCompanyUseCase,
    CreateEmployeeUseCase,
    FindEmployeeUseCase,
    UpdateEmployeeUseCase,
    DeleteEmployeeUseCase,
  ],
})
export class AdminModule {}
