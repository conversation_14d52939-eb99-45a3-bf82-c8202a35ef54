import { <PERSON>du<PERSON> } from '@nestjs/common';
import { UserController } from '../presentation/controllers';
import {
  CreateUserUseCase,
  FindUserUseCase,
  UpdateUserUseCase,
  DeleteUserUseCase,
} from '../application';
import { UserDomainService } from '../domain';
import { PrismaUserRepository } from '../infrastructure';
import { USER_REPOSITORY } from '../shared/tokens';

@Module({
  controllers: [UserController],
  providers: [
    // Use Cases
    CreateUserUseCase,
    FindUserUseCase,
    UpdateUserUseCase,
    DeleteUserUseCase,

    // Domain Services
    UserDomainService,

    // Repository Implementation
    {
      provide: USER_REPOSITORY,
      useClass: PrismaUserRepository,
    },
  ],
  exports: [
    CreateUserUseCase,
    FindUserUseCase,
    UpdateUserUseCase,
    DeleteUserUseCase,
  ],
})
export class UserModule {}
