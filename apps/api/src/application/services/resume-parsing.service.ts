import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';

export interface ExtractedResumeData {
  fullName?: string;
  email?: string;
  phone?: string;
  location?: string;
  jobTitle?: string;
  experience?: string;
  skills?: string;
  education?: string;
  summary?: string;
}

@Injectable()
export class ResumeParsingService {
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not configured');
    }

    this.openai = new OpenAI({
      apiKey,
    });
  }

  async extractTextFromFile(file: Express.Multer.File): Promise<string> {
    const { buffer, mimetype } = file;

    try {
      if (mimetype === 'application/pdf') {
        const pdfData = await pdfParse(buffer);
        return pdfData.text;
      } else if (
        mimetype === 'application/msword' ||
        mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        const result = await mammoth.extractRawText({ buffer });
        return result.value;
      } else {
        throw new Error('Unsupported file type');
      }
    } catch (error) {
      console.error('Error extracting text from file:', error);
      throw new Error('Failed to extract text from resume file');
    }
  }

  async extractDataFromText(text: string): Promise<ExtractedResumeData> {
    try {
      const prompt = `
        Extract the following information from this resume text and return it as a JSON object.
        If any information is not found, omit that field from the response.
        
        Fields to extract:
        - fullName: The person's full name
        - email: Email address
        - phone: Phone number (format as provided)
        - location: City, State or full address
        - jobTitle: Current or most recent job title
        - experience: Years of experience or experience level (e.g., "5 years", "Senior level")
        - skills: Key technical and professional skills (comma-separated)
        - education: Highest degree and institution
        - summary: Professional summary or objective (if present)

        Resume text:
        ${text}

        Return only valid JSON without any additional text or formatting.
      `;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a professional resume parser. Extract information accurately and return only valid JSON.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.1,
      });

      const responseContent = completion.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error('No response from OpenAI');
      }

      // Parse the JSON response
      const extractedData = JSON.parse(responseContent);
      return extractedData;
    } catch (error) {
      console.error('Error extracting data with OpenAI:', error);
      
      // Return empty object if AI extraction fails
      return {};
    }
  }

  async parseResume(file: Express.Multer.File): Promise<ExtractedResumeData> {
    // Extract text from the file
    const text = await this.extractTextFromFile(file);
    
    // Use AI to extract structured data
    const extractedData = await this.extractDataFromText(text);
    
    return extractedData;
  }
}
