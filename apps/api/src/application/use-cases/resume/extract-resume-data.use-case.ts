import { Injectable } from '@nestjs/common';
import { ResumeParsingService, ExtractedResumeData } from '../../services/resume-parsing.service';

@Injectable()
export class ExtractResumeDataUseCase {
  constructor(private readonly resumeParsingService: ResumeParsingService) {}

  async execute(file: Express.Multer.File): Promise<ExtractedResumeData> {
    return this.resumeParsingService.parseResume(file);
  }
}
