import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../../../database/database.service';

export interface SubmitResumeData {
  fullName: string;
  email: string;
  phone: string;
  location: string;
  jobTitle: string;
  experience: string;
  skills: string;
  education: string;
  summary?: string;
  fileName: string;
  fileSize: number;
  fileType: string;
}

export interface SubmitResumeResult {
  id: string;
  message: string;
  submittedAt: Date;
}

@Injectable()
export class SubmitResumeUseCase {
  constructor(private readonly databaseService: DatabaseService) {}

  async execute(data: SubmitResumeData): Promise<SubmitResumeResult> {
    try {
      // For now, we'll just log the data and return a success response
      // In a real application, you would save this to a database
      console.log('Resume submission data:', data);

      // You could save to database like this:
      // const resume = await this.databaseService.resume.create({
      //   data: {
      //     fullName: data.fullName,
      //     email: data.email,
      //     phone: data.phone,
      //     location: data.location,
      //     jobTitle: data.jobTitle,
      //     experience: data.experience,
      //     skills: data.skills,
      //     education: data.education,
      //     summary: data.summary,
      //     fileName: data.fileName,
      //     fileSize: data.fileSize,
      //     fileType: data.fileType,
      //     submittedAt: new Date(),
      //   },
      // });

      // For demo purposes, return a mock response
      const mockId = `resume_${Date.now()}`;
      const submittedAt = new Date();

      return {
        id: mockId,
        message: 'Resume submitted successfully',
        submittedAt,
      };
    } catch (error) {
      console.error('Error submitting resume:', error);
      throw new Error('Failed to submit resume');
    }
  }
}
