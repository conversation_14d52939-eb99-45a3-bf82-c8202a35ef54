import { Injectable, Inject } from '@nestjs/common';
import {
  User,
  UserRepository,
  Email,
  OAuthProvider,
  PictureUrl,
} from '../../../domain';
import { UserDomainService } from '../../../domain/services';
import { UserResponseDto } from '../../dtos';
import { USER_REPOSITORY } from '../../../shared/tokens';

export interface OAuthSignupDto {
  email: string;
  name?: string;
  provider: 'google' | 'linkedin';
  providerId: string;
  picture?: string;
}

@Injectable()
export class OAuthSignupUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  async execute(dto: OAuthSignupDto): Promise<UserResponseDto> {
    // Create domain objects
    const email = new Email(dto.email);
    const provider =
      dto.provider === 'google'
        ? OAuthProvider.google(dto.providerId)
        : OAuthProvider.linkedin(dto.providerId);
    const picture = dto.picture ? new PictureUrl(dto.picture) : undefined;

    // Check if user already exists with this OAuth provider
    const existingOAuthUser =
      await this.userRepository.findByOAuthProvider(provider);
    if (existingOAuthUser) {
      // User already exists with this OAuth provider, return existing user
      return existingOAuthUser.toPlainObject();
    }

    // Check if user already exists with this email
    const existingEmailUser = await this.userRepository.findByEmail(email);
    if (existingEmailUser) {
      // User exists with this email but different provider
      // For now, we'll throw an error. In production, you might want to link accounts
      throw new Error(
        `User with email ${email.value} already exists with a different provider`,
      );
    }

    // Create new user
    const user = User.createFromOAuth({
      email,
      name: dto.name,
      provider,
      picture,
    });

    // Apply domain rules (this will validate email uniqueness)
    this.userDomainService.validateUserForCreation(user, null);

    // Save to repository
    const savedUser = await this.userRepository.save(user);

    // Return response DTO
    return savedUser.toPlainObject();
  }
}
