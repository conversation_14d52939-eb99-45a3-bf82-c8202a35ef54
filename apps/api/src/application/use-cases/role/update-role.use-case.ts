import { Injectable, Inject } from '@nestjs/common';
import { RoleRepository } from '../../../domain';
import { RoleId, Permissions } from '../../../domain/value-objects';
import { UpdateRoleDto, RoleResponseDto } from '../../dtos';
import { ROLE_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class UpdateRoleUseCase {
  constructor(
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
  ) {}

  async execute(id: string, dto: UpdateRoleDto): Promise<RoleResponseDto> {
    const roleId = RoleId.fromString(id);
    const role = await this.roleRepository.findById(roleId);

    if (!role) {
      throw new NotFoundException('Role', id);
    }

    // Update role properties
    if (dto.description !== undefined) {
      role.updateDescription(dto.description);
    }

    if (dto.permissions !== undefined) {
      const permissions = new Permissions(dto.permissions);
      role.updatePermissions(permissions);
    }

    // Save updated role
    const updatedRole = await this.roleRepository.save(role);

    return updatedRole.toPlainObject();
  }
}
