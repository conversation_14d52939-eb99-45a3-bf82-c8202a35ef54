import { Injectable, Inject } from '@nestjs/common';
import { RoleRepository } from '../../../domain';
import { RoleId, RoleName } from '../../../domain/value-objects';
import { RoleResponseDto } from '../../dtos';
import { ROLE_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class FindRoleUseCase {
  constructor(
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
  ) {}

  async findById(id: string): Promise<RoleResponseDto> {
    const roleId = RoleId.fromString(id);
    const role = await this.roleRepository.findById(roleId);

    if (!role) {
      throw new NotFoundException('Role', id);
    }

    return role.toPlainObject();
  }

  async findByName(name: string): Promise<RoleResponseDto> {
    const roleName = new RoleName(name);
    const role = await this.roleRepository.findByName(roleName);

    if (!role) {
      throw new NotFoundException('Role', name);
    }

    return role.toPlainObject();
  }

  async findAll(): Promise<RoleResponseDto[]> {
    const roles = await this.roleRepository.findAll();
    return roles.map((role) => role.toPlainObject());
  }
}
