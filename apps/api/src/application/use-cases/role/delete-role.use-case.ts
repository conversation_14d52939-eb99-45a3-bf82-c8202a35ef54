import { Injectable, Inject } from '@nestjs/common';
import { RoleRepository } from '../../../domain';
import { RoleId } from '../../../domain/value-objects';
import { ROLE_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class DeleteRoleUseCase {
  constructor(
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const roleId = RoleId.fromString(id);
    const exists = await this.roleRepository.exists(roleId);

    if (!exists) {
      throw new NotFoundException('Role', id);
    }

    await this.roleRepository.delete(roleId);
  }
}
