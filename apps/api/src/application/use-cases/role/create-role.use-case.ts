import { Injectable, Inject } from '@nestjs/common';
import { Role, RoleRepository } from '../../../domain';
import { RoleName, Permissions } from '../../../domain/value-objects';
import { CreateRoleDto, RoleResponseDto } from '../../dtos';
import { ROLE_REPOSITORY } from '../../../shared/tokens';
import { ConflictException } from '../../../shared/exceptions';

@Injectable()
export class CreateRoleUseCase {
  constructor(
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
  ) {}

  async execute(dto: CreateRoleDto): Promise<RoleResponseDto> {
    // Create domain objects
    const roleName = new RoleName(dto.name);
    const permissions = dto.permissions 
      ? new Permissions(dto.permissions)
      : undefined;

    // Check if role already exists
    const existingRole = await this.roleRepository.findByName(roleName);
    if (existingRole) {
      throw new ConflictException(`Role with name '${dto.name}' already exists`);
    }

    // Create role
    const role = Role.create({
      name: roleName,
      description: dto.description,
      permissions,
    });

    // Save to repository
    const savedRole = await this.roleRepository.save(role);

    // Return response DTO
    return savedRole.toPlainObject();
  }
}
