import { Injectable, Inject } from '@nestjs/common';
import { UserId, Email, UserRepository } from '../../../domain';
import { UserResponseDto } from '../../dtos';
import { NotFoundException } from '../../../shared/exceptions';
import { USER_REPOSITORY } from '../../../shared/tokens';

@Injectable()
export class FindUserUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
  ) {}

  async findById(id: string): Promise<UserResponseDto> {
    const userId = UserId.fromString(id);
    const user = await this.userRepository.findById(userId);

    if (!user) {
      throw new NotFoundException('User', id);
    }

    return user.toPlainObject();
  }

  async findByEmail(email: string): Promise<UserResponseDto> {
    const emailVo = new Email(email);
    const user = await this.userRepository.findByEmail(emailVo);

    if (!user) {
      throw new NotFoundException('User', email);
    }

    return user.toPlainObject();
  }

  async findAll(): Promise<UserResponseDto[]> {
    const users = await this.userRepository.findAll();
    return users.map((user) => user.toPlainObject());
  }
}
