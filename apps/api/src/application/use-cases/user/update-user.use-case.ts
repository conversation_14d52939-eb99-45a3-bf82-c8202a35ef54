import { Injectable, Inject } from '@nestjs/common';
import {
  UserId,
  Email,
  UserRepository,
  UserDomainService,
} from '../../../domain';
import { UpdateUserDto, UserResponseDto } from '../../dtos';
import { NotFoundException } from '../../../shared/exceptions';
import { USER_REPOSITORY } from '../../../shared/tokens';

@Injectable()
export class UpdateUserUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  async execute(id: string, dto: UpdateUserDto): Promise<UserResponseDto> {
    // Find existing user
    const userId = UserId.fromString(id);
    const user = await this.userRepository.findById(userId);

    if (!user) {
      throw new NotFoundException('User', id);
    }

    // Update user properties
    if (dto.email) {
      const newEmail = new Email(dto.email);
      user.updateEmail(newEmail);

      // Check if new email already exists
      const existingUserWithEmail =
        await this.userRepository.findByEmail(newEmail);

      // Apply domain rules for email uniqueness
      this.userDomainService.validateUserForUpdate(user, existingUserWithEmail);
    }

    if (dto.name !== undefined) {
      user.updateName(dto.name);
    }

    if (dto.phoneNumber !== undefined) {
      user.updatePhoneNumber(dto.phoneNumber);
    }

    if (dto.birthDate !== undefined) {
      user.updateBirthDate(dto.birthDate);
    }

    if (dto.jobPosition !== undefined) {
      user.updateJobPosition(dto.jobPosition);
    }

    if (dto.address !== undefined) {
      user.updateAddress(dto.address);
    }

    // Save updated user
    const updatedUser = await this.userRepository.save(user);

    return updatedUser.toPlainObject();
  }
}
