import { Injectable, Inject } from '@nestjs/common';
import { EmployeeRepository } from '../../../domain';
import { EmployeeId } from '../../../domain/value-objects';
import { UpdateEmployeeDto, EmployeeResponseDto } from '../../dtos';
import { EMPLOYEE_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class UpdateEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  async execute(
    id: string,
    dto: UpdateEmployeeDto,
  ): Promise<EmployeeResponseDto> {
    const employeeId = EmployeeId.fromString(id);
    const employee = await this.employeeRepository.findById(employeeId);

    if (!employee) {
      throw new NotFoundException('Employee', id);
    }

    // Update employee properties
    if (dto.position !== undefined) {
      employee.updatePosition(dto.position);
    }

    if (dto.department !== undefined) {
      employee.updateDepartment(dto.department);
    }

    if (dto.salary !== undefined) {
      employee.updateSalary(dto.salary);
    }

    if (dto.startDate !== undefined) {
      employee.updateStartDate(dto.startDate);
    }

    if (dto.endDate !== undefined) {
      employee.updateEndDate(dto.endDate);
    }

    if (dto.status !== undefined) {
      employee.updateStatus(dto.status);
    }

    // Save updated employee
    const updatedEmployee = await this.employeeRepository.save(employee);

    return updatedEmployee.toPlainObject();
  }
}
