import { Injectable, Inject } from '@nestjs/common';
import {
  Employee,
  EmployeeRepository,
  UserRepository,
  CompanyRepository,
} from '../../../domain';
import { UserId, CompanyId } from '../../../domain/value-objects';
import { CreateEmployeeDto, EmployeeResponseDto } from '../../dtos';
import {
  EMPLOYEE_REPOSITORY,
  USER_REPOSITORY,
  COMPANY_REPOSITORY,
} from '../../../shared/tokens';
import {
  ConflictException,
  NotFoundException,
} from '../../../shared/exceptions';

@Injectable()
export class CreateEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepository,
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(dto: CreateEmployeeDto): Promise<EmployeeResponseDto> {
    const userId = UserId.fromString(dto.userId);
    const companyId = CompanyId.fromString(dto.companyId);

    // Validate user exists
    const userExists = await this.userRepository.exists(userId);
    if (!userExists) {
      throw new NotFoundException('User', dto.userId);
    }

    // Validate company exists
    const companyExists = await this.companyRepository.exists(companyId);
    if (!companyExists) {
      throw new NotFoundException('Company', dto.companyId);
    }

    // Check if user is already an employee
    const existingEmployee = await this.employeeRepository.findByUserId(userId);
    if (existingEmployee) {
      throw new ConflictException(`User is already an employee of a company`);
    }

    // Create employee
    const employee = Employee.create({
      userId,
      companyId,
      position: dto.position,
      department: dto.department,
      salary: dto.salary,
      startDate: dto.startDate,
      status: dto.status,
    });

    // Save to repository
    const savedEmployee = await this.employeeRepository.save(employee);

    // Return response DTO
    return savedEmployee.toPlainObject();
  }
}
