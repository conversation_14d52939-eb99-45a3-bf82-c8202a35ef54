import { Injectable, Inject } from '@nestjs/common';
import { EmployeeRepository } from '../../../domain';
import { EmployeeId } from '../../../domain/value-objects';
import { EMPLOYEE_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class DeleteEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const employeeId = EmployeeId.fromString(id);
    const exists = await this.employeeRepository.exists(employeeId);

    if (!exists) {
      throw new NotFoundException('Employee', id);
    }

    await this.employeeRepository.delete(employeeId);
  }
}
