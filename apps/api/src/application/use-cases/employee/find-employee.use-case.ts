import { Injectable, Inject } from '@nestjs/common';
import { EmployeeRepository } from '../../../domain';
import { EmployeeId, UserId, CompanyId } from '../../../domain/value-objects';
import { EmployeeResponseDto } from '../../dtos';
import { EMPLOYEE_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class FindEmployeeUseCase {
  constructor(
    @Inject(EMPLOYEE_REPOSITORY)
    private readonly employeeRepository: EmployeeRepository,
  ) {}

  async findById(id: string): Promise<EmployeeResponseDto> {
    const employeeId = EmployeeId.fromString(id);
    const employee = await this.employeeRepository.findById(employeeId);

    if (!employee) {
      throw new NotFoundException('Employee', id);
    }

    return employee.toPlainObject();
  }

  async findByUserId(userId: string): Promise<EmployeeResponseDto> {
    const userIdVO = UserId.fromString(userId);
    const employee = await this.employeeRepository.findByUserId(userIdVO);

    if (!employee) {
      throw new NotFoundException('Employee', userId);
    }

    return employee.toPlainObject();
  }

  async findByCompanyId(companyId: string): Promise<EmployeeResponseDto[]> {
    const companyIdVO = CompanyId.fromString(companyId);
    const employees =
      await this.employeeRepository.findByCompanyId(companyIdVO);
    return employees.map((employee) => employee.toPlainObject());
  }

  async findAll(): Promise<EmployeeResponseDto[]> {
    const employees = await this.employeeRepository.findAll();
    return employees.map((employee) => employee.toPlainObject());
  }
}
