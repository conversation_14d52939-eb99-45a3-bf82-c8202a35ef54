import { Injectable, Inject } from '@nestjs/common';
import { CompanyRepository } from '../../../domain';
import { CompanyId } from '../../../domain/value-objects';
import { CompanyResponseDto } from '../../dtos';
import { COMPANY_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class FindCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async findById(id: string): Promise<CompanyResponseDto> {
    const companyId = CompanyId.fromString(id);
    const company = await this.companyRepository.findById(companyId);

    if (!company) {
      throw new NotFoundException('Company', id);
    }

    return company.toPlainObject();
  }

  async findByName(name: string): Promise<CompanyResponseDto> {
    const company = await this.companyRepository.findByName(name);

    if (!company) {
      throw new NotFoundException('Company', name);
    }

    return company.toPlainObject();
  }

  async findAll(): Promise<CompanyResponseDto[]> {
    const companies = await this.companyRepository.findAll();
    return companies.map((company) => company.toPlainObject());
  }
}
