import { Injectable, Inject } from '@nestjs/common';
import { CompanyRepository } from '../../../domain';
import { CompanyId } from '../../../domain/value-objects';
import { COMPANY_REPOSITORY } from '../../../shared/tokens';
import { NotFoundException } from '../../../shared/exceptions';

@Injectable()
export class DeleteCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const companyId = CompanyId.fromString(id);
    const exists = await this.companyRepository.exists(companyId);

    if (!exists) {
      throw new NotFoundException('Company', id);
    }

    await this.companyRepository.delete(companyId);
  }
}
