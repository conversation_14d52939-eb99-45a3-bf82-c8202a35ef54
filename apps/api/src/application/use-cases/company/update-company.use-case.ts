import { Injectable, Inject } from '@nestjs/common';
import { CompanyRepository } from '../../../domain';
import { CompanyId } from '../../../domain/value-objects';
import { UpdateCompanyDto, CompanyResponseDto } from '../../dtos';
import { COMPANY_REPOSITORY } from '../../../shared/tokens';
import {
  NotFoundException,
  ConflictException,
} from '../../../shared/exceptions';

@Injectable()
export class UpdateCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(
    id: string,
    dto: UpdateCompanyDto,
  ): Promise<CompanyResponseDto> {
    const companyId = CompanyId.fromString(id);
    const company = await this.companyRepository.findById(companyId);

    if (!company) {
      throw new NotFoundException('Company', id);
    }

    // Check if name is being updated and if it conflicts
    if (dto.name && dto.name !== company.name) {
      const existingCompany = await this.companyRepository.findByName(dto.name);
      if (existingCompany && !existingCompany.id.equals(companyId)) {
        throw new ConflictException(
          `Company with name '${dto.name}' already exists`,
        );
      }
    }

    // Update company properties
    if (dto.name !== undefined) {
      company.updateName(dto.name);
    }

    if (dto.description !== undefined) {
      company.updateDescription(dto.description);
    }

    if (dto.website !== undefined) {
      company.updateWebsite(dto.website);
    }

    if (dto.industry !== undefined) {
      company.updateIndustry(dto.industry);
    }

    if (dto.size !== undefined) {
      company.updateSize(dto.size);
    }

    // Save updated company
    const updatedCompany = await this.companyRepository.save(company);

    return updatedCompany.toPlainObject();
  }
}
