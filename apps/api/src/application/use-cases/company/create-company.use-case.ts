import { Injectable, Inject } from '@nestjs/common';
import { Company, CompanyRepository } from '../../../domain';
import { CreateCompanyDto, CompanyResponseDto } from '../../dtos';
import { COMPANY_REPOSITORY } from '../../../shared/tokens';
import { ConflictException } from '../../../shared/exceptions';

@Injectable()
export class CreateCompanyUseCase {
  constructor(
    @Inject(COMPANY_REPOSITORY)
    private readonly companyRepository: CompanyRepository,
  ) {}

  async execute(dto: CreateCompanyDto): Promise<CompanyResponseDto> {
    // Check if company already exists
    const existingCompany = await this.companyRepository.findByName(dto.name);
    if (existingCompany) {
      throw new ConflictException(`Company with name '${dto.name}' already exists`);
    }

    // Create company
    const company = Company.create({
      name: dto.name,
      description: dto.description,
      website: dto.website,
      industry: dto.industry,
      size: dto.size,
    });

    // Save to repository
    const savedCompany = await this.companyRepository.save(company);

    // Return response DTO
    return savedCompany.toPlainObject();
  }
}
