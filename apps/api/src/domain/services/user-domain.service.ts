import { Injectable } from '@nestjs/common';
import { User } from '../entities';
import { Email } from '../value-objects';
import { ConflictException } from '../../shared/exceptions';

@Injectable()
export class UserDomainService {
  validateEmailIsUnique(
    email: Email,
    existingUser: User | null,
    excludeUserId?: string,
  ): void {
    if (existingUser && existingUser.id.value !== excludeUserId) {
      throw new ConflictException(
        `User with email ${email.value} already exists`,
      );
    }
  }

  validateUserForCreation(user: User, existingUser: User | null): void {
    this.validateEmailIsUnique(user.email, existingUser);
  }

  validateUserForUpdate(user: User, existingUser: User | null): void {
    this.validateEmailIsUnique(user.email, existingUser, user.id.value);
  }
}
