import { Email } from './email.vo';

describe('Email Value Object', () => {
  describe('constructor', () => {
    it('should create email with valid email address', () => {
      const email = new Email('<EMAIL>');
      expect(email.value).toBe('<EMAIL>');
    });

    it('should normalize email to lowercase', () => {
      const email = new Email('<EMAIL>');
      expect(email.value).toBe('<EMAIL>');
    });

    it('should trim whitespace', () => {
      const email = new Email('  <EMAIL>  ');
      expect(email.value).toBe('<EMAIL>');
    });

    it('should throw error for empty email', () => {
      expect(() => new Email('')).toThrow('Email cannot be empty');
      expect(() => new Email('   ')).toThrow('Email cannot be empty');
    });

    it('should throw error for invalid email format', () => {
      expect(() => new Email('invalid-email')).toThrow('Invalid email format');
      expect(() => new Email('test@')).toThrow('Invalid email format');
      expect(() => new Email('@example.com')).toThrow('Invalid email format');
    });

    it('should throw error for email that is too long', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      expect(() => new Email(longEmail)).toThrow('Email is too long');
    });
  });

  describe('equals', () => {
    it('should return true for equal emails', () => {
      const email1 = new Email('<EMAIL>');
      const email2 = new Email('<EMAIL>');
      
      expect(email1.equals(email2)).toBe(true);
    });

    it('should return false for different emails', () => {
      const email1 = new Email('<EMAIL>');
      const email2 = new Email('<EMAIL>');
      
      expect(email1.equals(email2)).toBe(false);
    });
  });

  describe('toString', () => {
    it('should return email value as string', () => {
      const email = new Email('<EMAIL>');
      expect(email.toString()).toBe('<EMAIL>');
    });
  });
});
