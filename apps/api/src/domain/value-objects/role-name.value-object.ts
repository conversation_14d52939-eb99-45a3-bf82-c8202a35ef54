export class RoleName {
  private readonly _value: string;

  constructor(value: string) {
    this.validate(value);
    this._value = value.toLowerCase();
  }

  get value(): string {
    return this._value;
  }

  private validate(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new Error('Role name cannot be empty');
    }

    if (value.length > 50) {
      throw new Error('Role name cannot exceed 50 characters');
    }

    const validRoles = ['admin', 'employer', 'employee', 'guest'];
    if (!validRoles.includes(value.toLowerCase())) {
      throw new Error(`Invalid role name. Must be one of: ${validRoles.join(', ')}`);
    }
  }

  equals(other: RoleName): boolean {
    return this._value === other._value;
  }

  static admin(): RoleName {
    return new RoleName('admin');
  }

  static employer(): <PERSON><PERSON><PERSON> {
    return new <PERSON><PERSON><PERSON>('employer');
  }

  static employee(): <PERSON><PERSON><PERSON> {
    return new <PERSON><PERSON><PERSON>('employee');
  }

  static guest(): <PERSON><PERSON>ame {
    return new <PERSON><PERSON><PERSON>('guest');
  }
}
