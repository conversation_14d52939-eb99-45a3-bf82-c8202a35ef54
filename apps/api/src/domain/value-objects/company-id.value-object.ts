import { createId } from '@paralleldrive/cuid2';

export class CompanyId {
  private readonly _value: string;

  constructor(value?: string) {
    this._value = value ?? createId();
    this.validate();
  }

  get value(): string {
    return this._value;
  }

  private validate(): void {
    if (!this._value || this._value.trim().length === 0) {
      throw new Error('Company ID cannot be empty');
    }

    // Basic CUID validation
    if (this._value.length < 7 || this._value.length > 32) {
      throw new Error('Invalid Company ID format');
    }
  }

  equals(other: CompanyId): boolean {
    return this._value === other._value;
  }

  static fromString(value: string): CompanyId {
    return new CompanyId(value);
  }
}
