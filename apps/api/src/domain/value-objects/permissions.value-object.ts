export type Permission = 
  | 'user:read'
  | 'user:write'
  | 'user:delete'
  | 'role:read'
  | 'role:write'
  | 'role:delete'
  | 'company:read'
  | 'company:write'
  | 'company:delete'
  | 'employee:read'
  | 'employee:write'
  | 'employee:delete'
  | 'admin:access'
  | 'reports:view';

export class Permissions {
  private readonly _permissions: Permission[];

  constructor(permissions: Permission[] = []) {
    this.validate(permissions);
    this._permissions = [...new Set(permissions)]; // Remove duplicates
  }

  get permissions(): Permission[] {
    return [...this._permissions];
  }

  private validate(permissions: Permission[]): void {
    const validPermissions: Permission[] = [
      'user:read', 'user:write', 'user:delete',
      'role:read', 'role:write', 'role:delete',
      'company:read', 'company:write', 'company:delete',
      'employee:read', 'employee:write', 'employee:delete',
      'admin:access', 'reports:view'
    ];

    for (const permission of permissions) {
      if (!validPermissions.includes(permission)) {
        throw new Error(`Invalid permission: ${permission}`);
      }
    }
  }

  hasPermission(permission: Permission): boolean {
    return this._permissions.includes(permission);
  }

  hasAnyPermission(permissions: Permission[]): boolean {
    return permissions.some(permission => this._permissions.includes(permission));
  }

  hasAllPermissions(permissions: Permission[]): boolean {
    return permissions.every(permission => this._permissions.includes(permission));
  }

  addPermission(permission: Permission): Permissions {
    if (this._permissions.includes(permission)) {
      return this;
    }
    return new Permissions([...this._permissions, permission]);
  }

  removePermission(permission: Permission): Permissions {
    return new Permissions(this._permissions.filter(p => p !== permission));
  }

  equals(other: Permissions): boolean {
    if (this._permissions.length !== other._permissions.length) {
      return false;
    }
    return this._permissions.every(permission => other._permissions.includes(permission));
  }

  static adminPermissions(): Permissions {
    return new Permissions([
      'user:read', 'user:write', 'user:delete',
      'role:read', 'role:write', 'role:delete',
      'company:read', 'company:write', 'company:delete',
      'employee:read', 'employee:write', 'employee:delete',
      'admin:access', 'reports:view'
    ]);
  }

  static employerPermissions(): Permissions {
    return new Permissions([
      'employee:read', 'employee:write',
      'company:read', 'company:write',
      'reports:view'
    ]);
  }

  static employeePermissions(): Permissions {
    return new Permissions([
      'user:read'
    ]);
  }

  static guestPermissions(): Permissions {
    return new Permissions([]);
  }
}
