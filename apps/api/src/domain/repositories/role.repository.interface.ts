import { Role } from '../entities';
import { RoleId, RoleName } from '../value-objects';

export interface RoleRepository {
  save(role: Role): Promise<Role>;
  findById(id: RoleId): Promise<Role | null>;
  findByName(name: <PERSON>Name): Promise<Role | null>;
  findAll(): Promise<Role[]>;
  delete(id: RoleId): Promise<void>;
  exists(id: RoleId): Promise<boolean>;
  existsByName(name: RoleName): Promise<boolean>;
}
