import { Employee } from '../entities';
import { EmployeeId, UserId, CompanyId } from '../value-objects';

export interface EmployeeRepository {
  save(employee: Employee): Promise<Employee>;
  findById(id: EmployeeId): Promise<Employee | null>;
  findByUserId(userId: UserId): Promise<Employee | null>;
  findByCompanyId(companyId: CompanyId): Promise<Employee[]>;
  findAll(): Promise<Employee[]>;
  delete(id: EmployeeId): Promise<void>;
  exists(id: EmployeeId): Promise<boolean>;
  existsByUserId(userId: UserId): Promise<boolean>;
}
