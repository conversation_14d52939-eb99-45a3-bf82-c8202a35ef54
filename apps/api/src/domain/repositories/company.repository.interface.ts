import { Company } from '../entities';
import { CompanyId } from '../value-objects';

export interface CompanyRepository {
  save(company: Company): Promise<Company>;
  findById(id: CompanyId): Promise<Company | null>;
  findByName(name: string): Promise<Company | null>;
  findAll(): Promise<Company[]>;
  delete(id: CompanyId): Promise<void>;
  exists(id: CompanyId): Promise<boolean>;
  existsByName(name: string): Promise<boolean>;
}
