import { Email, UserId, OAuth<PERSON>rovider, PictureUrl } from '../value-objects';

export interface UserProps {
  id?: UserId;
  email: Email;
  name?: string;
  provider?: OAuthProvider;
  picture?: PictureUrl;
  createdAt?: Date;
  updatedAt?: Date;
}

export class User {
  private readonly _id: UserId;
  private _email: Email;
  private _name?: string;
  private readonly _provider: OAuthProvider;
  private readonly _picture?: PictureUrl;
  private readonly _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: UserProps) {
    this._id = props.id || new UserId();
    this._email = props.email;
    this._name = props.name;
    this._provider = props.provider || OAuthProvider.local();
    this._picture = props.picture;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  get id(): UserId {
    return this._id;
  }

  get email(): Email {
    return this._email;
  }

  get name(): string | undefined {
    return this._name;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  get provider(): OAuthProvider {
    return this._provider;
  }

  get picture(): PictureUrl | undefined {
    return this._picture;
  }

  updateEmail(email: Email): void {
    if (!email) {
      throw new Error('Email is required');
    }
    this._email = email;
    this.touch();
  }

  updateName(name?: string): void {
    if (name !== undefined && name.trim().length === 0) {
      throw new Error('Name cannot be empty string');
    }
    this._name = name?.trim();
    this.touch();
  }

  private touch(): void {
    this._updatedAt = new Date();
  }

  private validate(): void {
    if (!this._email) {
      throw new Error('Email is required');
    }

    if (this._name !== undefined && this._name.trim().length === 0) {
      throw new Error('Name cannot be empty string');
    }

    if (this._name && this._name.length > 100) {
      throw new Error('Name is too long');
    }
  }

  equals(other: User): boolean {
    return this._id.equals(other._id);
  }

  toPlainObject(): {
    id: string;
    email: string;
    name?: string;
    provider: string;
    providerId?: string;
    picture?: string;
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this._id.value,
      email: this._email.value,
      name: this._name,
      provider: this._provider.type,
      providerId: this._provider.providerId,
      picture: this._picture?.value,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
    };
  }

  static create(
    props: Omit<UserProps, 'id' | 'createdAt' | 'updatedAt'>,
  ): User {
    return new User(props);
  }

  static createFromOAuth(props: {
    email: Email;
    name?: string;
    provider: OAuthProvider;
    picture?: PictureUrl;
  }): User {
    return new User({
      email: props.email,
      name: props.name,
      provider: props.provider,
      picture: props.picture,
    });
  }

  static fromPersistence(props: {
    id: string;
    email: string;
    name?: string | null;
    provider?: string | null;
    providerId?: string | null;
    picture?: string | null;
    createdAt: Date;
    updatedAt: Date;
  }): User {
    let provider: OAuthProvider;
    if (props.provider === 'google' && props.providerId) {
      provider = OAuthProvider.google(props.providerId);
    } else if (props.provider === 'linkedin' && props.providerId) {
      provider = OAuthProvider.linkedin(props.providerId);
    } else {
      provider = OAuthProvider.local();
    }

    return new User({
      id: UserId.fromString(props.id),
      email: new Email(props.email),
      name: props.name ?? undefined,
      provider,
      picture: props.picture ? new PictureUrl(props.picture) : undefined,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    });
  }
}
