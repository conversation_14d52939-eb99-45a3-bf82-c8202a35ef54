import { RoleId, RoleName, Permissions, Permission } from '../value-objects';

export interface RoleProps {
  id?: RoleId;
  name: RoleName;
  description?: string;
  permissions?: Permissions;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Role {
  private readonly _id: RoleId;
  private _name: RoleName;
  private _description?: string;
  private _permissions: Permissions;
  private readonly _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: RoleProps) {
    this._id = props.id || new RoleId();
    this._name = props.name;
    this._description = props.description;
    this._permissions = props.permissions || this.getDefaultPermissions(props.name);
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  get id(): RoleId {
    return this._id;
  }

  get name(): RoleName {
    return this._name;
  }

  get description(): string | undefined {
    return this._description;
  }

  get permissions(): Permissions {
    return this._permissions;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  private validate(): void {
    if (!this._name) {
      throw new Error('Role name is required');
    }

    if (this._description !== undefined && this._description.length > 500) {
      throw new Error('Role description cannot exceed 500 characters');
    }
  }

  private getDefaultPermissions(roleName: RoleName): Permissions {
    switch (roleName.value) {
      case 'admin':
        return Permissions.adminPermissions();
      case 'employer':
        return Permissions.employerPermissions();
      case 'employee':
        return Permissions.employeePermissions();
      case 'guest':
        return Permissions.guestPermissions();
      default:
        return new Permissions([]);
    }
  }

  updateDescription(description: string): void {
    if (description.length > 500) {
      throw new Error('Role description cannot exceed 500 characters');
    }
    this._description = description;
    this._updatedAt = new Date();
  }

  updatePermissions(permissions: Permissions): void {
    this._permissions = permissions;
    this._updatedAt = new Date();
  }

  hasPermission(permission: Permission): boolean {
    return this._permissions.hasPermission(permission);
  }

  hasAnyPermission(permissions: Permission[]): boolean {
    return this._permissions.hasAnyPermission(permissions);
  }

  hasAllPermissions(permissions: Permission[]): boolean {
    return this._permissions.hasAllPermissions(permissions);
  }

  equals(other: Role): boolean {
    return this._id.equals(other._id);
  }

  toPlainObject(): {
    id: string;
    name: string;
    description?: string;
    permissions: Permission[];
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this._id.value,
      name: this._name.value,
      description: this._description,
      permissions: this._permissions.permissions,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
    };
  }

  static create(props: Omit<RoleProps, 'id' | 'createdAt' | 'updatedAt'>): Role {
    return new Role(props);
  }

  static fromPersistence(props: {
    id: string;
    name: string;
    description?: string | null;
    permissions?: any;
    createdAt: Date;
    updatedAt: Date;
  }): Role {
    let permissions: Permissions;
    
    if (props.permissions && Array.isArray(props.permissions)) {
      permissions = new Permissions(props.permissions as Permission[]);
    } else {
      // Fallback to default permissions based on role name
      const roleName = new RoleName(props.name);
      permissions = new Role({ name: roleName }).getDefaultPermissions(roleName);
    }

    return new Role({
      id: RoleId.fromString(props.id),
      name: new RoleName(props.name),
      description: props.description ?? undefined,
      permissions,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    });
  }
}
