import { EmployeeId, UserId, CompanyId } from '../value-objects';

export interface EmployeeProps {
  id?: EmployeeId;
  userId: UserId;
  companyId: CompanyId;
  position?: string;
  department?: string;
  salary?: number;
  startDate?: Date;
  endDate?: Date;
  status?: 'active' | 'inactive' | 'terminated';
  createdAt?: Date;
  updatedAt?: Date;
}

export class Employee {
  private readonly _id: EmployeeId;
  private readonly _userId: UserId;
  private readonly _companyId: CompanyId;
  private _position?: string;
  private _department?: string;
  private _salary?: number;
  private _startDate?: Date;
  private _endDate?: Date;
  private _status: 'active' | 'inactive' | 'terminated';
  private readonly _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: EmployeeProps) {
    this._id = props.id || new EmployeeId();
    this._userId = props.userId;
    this._companyId = props.companyId;
    this._position = props.position;
    this._department = props.department;
    this._salary = props.salary;
    this._startDate = props.startDate;
    this._endDate = props.endDate;
    this._status = props.status || 'active';
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  get id(): EmployeeId {
    return this._id;
  }

  get userId(): UserId {
    return this._userId;
  }

  get companyId(): CompanyId {
    return this._companyId;
  }

  get position(): string | undefined {
    return this._position;
  }

  get department(): string | undefined {
    return this._department;
  }

  get salary(): number | undefined {
    return this._salary;
  }

  get startDate(): Date | undefined {
    return this._startDate;
  }

  get endDate(): Date | undefined {
    return this._endDate;
  }

  get status(): 'active' | 'inactive' | 'terminated' {
    return this._status;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  private validate(): void {
    if (!this._userId) {
      throw new Error('User ID is required');
    }

    if (!this._companyId) {
      throw new Error('Company ID is required');
    }

    if (this._position && this._position.length > 100) {
      throw new Error('Position cannot exceed 100 characters');
    }

    if (this._department && this._department.length > 100) {
      throw new Error('Department cannot exceed 100 characters');
    }

    if (this._salary && this._salary < 0) {
      throw new Error('Salary cannot be negative');
    }

    if (this._startDate && this._endDate && this._startDate > this._endDate) {
      throw new Error('Start date cannot be after end date');
    }

    const validStatuses = ['active', 'inactive', 'terminated'];
    if (!validStatuses.includes(this._status)) {
      throw new Error(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }
  }

  updatePosition(position?: string): void {
    if (position && position.length > 100) {
      throw new Error('Position cannot exceed 100 characters');
    }
    this._position = position;
    this._updatedAt = new Date();
  }

  updateDepartment(department?: string): void {
    if (department && department.length > 100) {
      throw new Error('Department cannot exceed 100 characters');
    }
    this._department = department;
    this._updatedAt = new Date();
  }

  updateSalary(salary?: number): void {
    if (salary && salary < 0) {
      throw new Error('Salary cannot be negative');
    }
    this._salary = salary;
    this._updatedAt = new Date();
  }

  updateStartDate(startDate?: Date): void {
    if (startDate && this._endDate && startDate > this._endDate) {
      throw new Error('Start date cannot be after end date');
    }
    this._startDate = startDate;
    this._updatedAt = new Date();
  }

  updateEndDate(endDate?: Date): void {
    if (endDate && this._startDate && this._startDate > endDate) {
      throw new Error('End date cannot be before start date');
    }
    this._endDate = endDate;
    this._updatedAt = new Date();
  }

  updateStatus(status: 'active' | 'inactive' | 'terminated'): void {
    const validStatuses = ['active', 'inactive', 'terminated'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }
    this._status = status;
    this._updatedAt = new Date();
  }

  terminate(endDate?: Date): void {
    this._status = 'terminated';
    if (endDate) {
      this.updateEndDate(endDate);
    }
    this._updatedAt = new Date();
  }

  isActive(): boolean {
    return this._status === 'active';
  }

  equals(other: Employee): boolean {
    return this._id.equals(other._id);
  }

  toPlainObject(): {
    id: string;
    userId: string;
    companyId: string;
    position?: string;
    department?: string;
    salary?: number;
    startDate?: Date;
    endDate?: Date;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this._id.value,
      userId: this._userId.value,
      companyId: this._companyId.value,
      position: this._position,
      department: this._department,
      salary: this._salary,
      startDate: this._startDate,
      endDate: this._endDate,
      status: this._status,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
    };
  }

  static create(props: Omit<EmployeeProps, 'id' | 'createdAt' | 'updatedAt'>): Employee {
    return new Employee(props);
  }

  static fromPersistence(props: {
    id: string;
    userId: string;
    companyId: string;
    position?: string | null;
    department?: string | null;
    salary?: any;
    startDate?: Date | null;
    endDate?: Date | null;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  }): Employee {
    return new Employee({
      id: EmployeeId.fromString(props.id),
      userId: UserId.fromString(props.userId),
      companyId: CompanyId.fromString(props.companyId),
      position: props.position ?? undefined,
      department: props.department ?? undefined,
      salary: props.salary ? Number(props.salary) : undefined,
      startDate: props.startDate ?? undefined,
      endDate: props.endDate ?? undefined,
      status: props.status as 'active' | 'inactive' | 'terminated',
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    });
  }
}
