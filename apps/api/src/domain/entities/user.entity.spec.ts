import { User } from './user.entity';
import { Email, UserId } from '../value-objects';

describe('User Entity', () => {
  describe('create', () => {
    it('should create a user with valid data', () => {
      const email = new Email('<EMAIL>');
      const user = User.create({ email, name: 'Test User' });

      expect(user.email.value).toBe('<EMAIL>');
      expect(user.name).toBe('Test User');
      expect(user.id).toBeInstanceOf(UserId);
      expect(user.createdAt).toBeInstanceOf(Date);
      expect(user.updatedAt).toBeInstanceOf(Date);
    });

    it('should create a user without name', () => {
      const email = new Email('<EMAIL>');
      const user = User.create({ email });

      expect(user.email.value).toBe('<EMAIL>');
      expect(user.name).toBeUndefined();
    });
  });

  describe('updateEmail', () => {
    it('should update email and touch updatedAt', () => {
      const user = User.create({ email: new Email('<EMAIL>') });
      const originalUpdatedAt = user.updatedAt;
      
      // Wait a bit to ensure timestamp difference
      setTimeout(() => {
        const newEmail = new Email('<EMAIL>');
        user.updateEmail(newEmail);

        expect(user.email.value).toBe('<EMAIL>');
        expect(user.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
      }, 1);
    });
  });

  describe('updateName', () => {
    it('should update name and touch updatedAt', () => {
      const user = User.create({ email: new Email('<EMAIL>'), name: 'Old Name' });
      const originalUpdatedAt = user.updatedAt;
      
      setTimeout(() => {
        user.updateName('New Name');

        expect(user.name).toBe('New Name');
        expect(user.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
      }, 1);
    });

    it('should throw error for empty string name', () => {
      const user = User.create({ email: new Email('<EMAIL>') });

      expect(() => user.updateName('')).toThrow('Name cannot be empty string');
    });
  });

  describe('fromPersistence', () => {
    it('should create user from persistence data', () => {
      const persistenceData = {
        id: 'c123456789012345678901234',
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02'),
      };

      const user = User.fromPersistence(persistenceData);

      expect(user.id.value).toBe('c123456789012345678901234');
      expect(user.email.value).toBe('<EMAIL>');
      expect(user.name).toBe('Test User');
      expect(user.createdAt).toEqual(new Date('2023-01-01'));
      expect(user.updatedAt).toEqual(new Date('2023-01-02'));
    });

    it('should handle null name from persistence', () => {
      const persistenceData = {
        id: 'c123456789012345678901234',
        email: '<EMAIL>',
        name: null,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02'),
      };

      const user = User.fromPersistence(persistenceData);

      expect(user.name).toBeUndefined();
    });
  });

  describe('toPlainObject', () => {
    it('should convert to plain object', () => {
      const user = User.create({ 
        email: new Email('<EMAIL>'), 
        name: 'Test User' 
      });

      const plainObject = user.toPlainObject();

      expect(plainObject).toEqual({
        id: user.id.value,
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      });
    });
  });
});
