import { CompanyId } from '../value-objects';

export interface CompanyProps {
  id?: CompanyId;
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  createdAt?: Date;
  updatedAt?: Date;
}

export class Company {
  private readonly _id: CompanyId;
  private _name: string;
  private _description?: string;
  private _website?: string;
  private _industry?: string;
  private _size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  private readonly _createdAt: Date;
  private _updatedAt: Date;

  constructor(props: CompanyProps) {
    this._id = props.id || new CompanyId();
    this._name = props.name;
    this._description = props.description;
    this._website = props.website;
    this._industry = props.industry;
    this._size = props.size;
    this._createdAt = props.createdAt || new Date();
    this._updatedAt = props.updatedAt || new Date();

    this.validate();
  }

  get id(): CompanyId {
    return this._id;
  }

  get name(): string {
    return this._name;
  }

  get description(): string | undefined {
    return this._description;
  }

  get website(): string | undefined {
    return this._website;
  }

  get industry(): string | undefined {
    return this._industry;
  }

  get size(): 'startup' | 'small' | 'medium' | 'large' | 'enterprise' | undefined {
    return this._size;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  private validate(): void {
    if (!this._name || this._name.trim().length === 0) {
      throw new Error('Company name is required');
    }

    if (this._name.length > 200) {
      throw new Error('Company name cannot exceed 200 characters');
    }

    if (this._description && this._description.length > 1000) {
      throw new Error('Company description cannot exceed 1000 characters');
    }

    if (this._website && !this.isValidUrl(this._website)) {
      throw new Error('Invalid website URL format');
    }

    if (this._industry && this._industry.length > 100) {
      throw new Error('Industry cannot exceed 100 characters');
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  updateName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error('Company name is required');
    }
    if (name.length > 200) {
      throw new Error('Company name cannot exceed 200 characters');
    }
    this._name = name;
    this._updatedAt = new Date();
  }

  updateDescription(description?: string): void {
    if (description && description.length > 1000) {
      throw new Error('Company description cannot exceed 1000 characters');
    }
    this._description = description;
    this._updatedAt = new Date();
  }

  updateWebsite(website?: string): void {
    if (website && !this.isValidUrl(website)) {
      throw new Error('Invalid website URL format');
    }
    this._website = website;
    this._updatedAt = new Date();
  }

  updateIndustry(industry?: string): void {
    if (industry && industry.length > 100) {
      throw new Error('Industry cannot exceed 100 characters');
    }
    this._industry = industry;
    this._updatedAt = new Date();
  }

  updateSize(size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'): void {
    this._size = size;
    this._updatedAt = new Date();
  }

  equals(other: Company): boolean {
    return this._id.equals(other._id);
  }

  toPlainObject(): {
    id: string;
    name: string;
    description?: string;
    website?: string;
    industry?: string;
    size?: string;
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this._id.value,
      name: this._name,
      description: this._description,
      website: this._website,
      industry: this._industry,
      size: this._size,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
    };
  }

  static create(props: Omit<CompanyProps, 'id' | 'createdAt' | 'updatedAt'>): Company {
    return new Company(props);
  }

  static fromPersistence(props: {
    id: string;
    name: string;
    description?: string | null;
    website?: string | null;
    industry?: string | null;
    size?: string | null;
    createdAt: Date;
    updatedAt: Date;
  }): Company {
    return new Company({
      id: CompanyId.fromString(props.id),
      name: props.name,
      description: props.description ?? undefined,
      website: props.website ?? undefined,
      industry: props.industry ?? undefined,
      size: props.size as 'startup' | 'small' | 'medium' | 'large' | 'enterprise' | undefined,
      createdAt: props.createdAt,
      updatedAt: props.updatedAt,
    });
  }
}
