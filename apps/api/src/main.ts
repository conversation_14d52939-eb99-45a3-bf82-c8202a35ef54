import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS
  app.enableCors({
    origin: [
      'http://localhost:3000', // Frontend URL
      process.env.FRONTEND_URL, // From environment variable
    ].filter(Boolean), // Remove undefined values
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true, // Allow cookies/credentials
  });

  const port = process.env.PORT ?? 4000;
  await app.listen(port);
  console.log(`🚀 API Server running on http://localhost:${port}`);
}
bootstrap();
