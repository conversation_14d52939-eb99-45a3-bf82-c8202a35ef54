import { IsString, IsOptional, IsIn, IsUrl } from 'class-validator';

export class UpdateCompanyRequest {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsUrl()
  website?: string;

  @IsOptional()
  @IsString()
  industry?: string;

  @IsOptional()
  @IsIn(['startup', 'small', 'medium', 'large', 'enterprise'])
  size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
}
