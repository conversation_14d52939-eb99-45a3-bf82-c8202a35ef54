import { IsString, IsOptional, <PERSON><PERSON><PERSON><PERSON>, IsIn } from 'class-validator';
import { Permission } from '../../../domain/value-objects';

export class CreateRoleRequest {
  @IsString()
  @IsIn(['admin', 'employer', 'employee', 'guest'])
  name: 'admin' | 'employer' | 'employee' | 'guest';

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  permissions?: Permission[];
}
