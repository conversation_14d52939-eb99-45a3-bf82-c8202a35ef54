import {
  IsString,
  IsOptional,
  IsN<PERSON>ber,
  IsDateString,
  IsIn,
} from 'class-validator';

export class UpdateEmployeeRequest {
  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsNumber()
  salary?: number;

  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsOptional()
  @IsIn(['active', 'inactive', 'terminated'])
  status?: 'active' | 'inactive' | 'terminated';
}
