import { RoleResponseDto } from '../../application/dtos';
import { Permission } from '../../domain/value-objects';

export class RoleResponse {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  createdAt: Date;
  updatedAt: Date;

  constructor(dto: RoleResponseDto) {
    this.id = dto.id;
    this.name = dto.name;
    this.description = dto.description;
    this.permissions = dto.permissions;
    this.createdAt = dto.createdAt;
    this.updatedAt = dto.updatedAt;
  }
}
