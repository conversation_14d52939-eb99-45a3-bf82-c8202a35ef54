import { EmployeeResponseDto } from '../../application/dtos';

export class EmployeeResponse {
  id: string;
  userId: string;
  companyId: string;
  position?: string;
  department?: string;
  salary?: number;
  startDate?: Date;
  endDate?: Date;
  status: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(dto: EmployeeResponseDto) {
    this.id = dto.id;
    this.userId = dto.userId;
    this.companyId = dto.companyId;
    this.position = dto.position;
    this.department = dto.department;
    this.salary = dto.salary;
    this.startDate = dto.startDate;
    this.endDate = dto.endDate;
    this.status = dto.status;
    this.createdAt = dto.createdAt;
    this.updatedAt = dto.updatedAt;
  }
}
