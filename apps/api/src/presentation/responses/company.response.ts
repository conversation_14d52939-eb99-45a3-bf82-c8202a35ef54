import { CompanyResponseDto } from '../../application/dtos';

export class CompanyResponse {
  id: string;
  name: string;
  description?: string;
  website?: string;
  industry?: string;
  size?: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(dto: CompanyResponseDto) {
    this.id = dto.id;
    this.name = dto.name;
    this.description = dto.description;
    this.website = dto.website;
    this.industry = dto.industry;
    this.size = dto.size;
    this.createdAt = dto.createdAt;
    this.updatedAt = dto.updatedAt;
  }
}
