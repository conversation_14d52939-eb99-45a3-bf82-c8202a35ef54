import { UserResponseDto } from '../../application/dtos';

export class UserResponse {
  id: string;
  email: string;
  name?: string;
  provider?: string;
  providerId?: string;
  picture?: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(dto: UserResponseDto) {
    this.id = dto.id;
    this.email = dto.email;
    this.name = dto.name;
    this.provider = dto.provider;
    this.providerId = dto.providerId;
    this.picture = dto.picture;
    this.createdAt = dto.createdAt;
    this.updatedAt = dto.updatedAt;
  }
}
