import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  // User use cases
  FindUserUseCase,
  UpdateUserUseCase,
  DeleteUserUseCase,
  // Role use cases
  Create<PERSON><PERSON>UseCase,
  FindRoleUseCase,
  UpdateRoleUseCase,
  DeleteRoleUseCase,
  // Company use cases
  CreateCompanyUseCase,
  FindCompanyUseCase,
  UpdateCompanyUseCase,
  DeleteCompanyUseCase,
  // Employee use cases
  CreateEmployeeUseCase,
  FindEmployeeUseCase,
  UpdateEmployeeUseCase,
  DeleteEmployeeUseCase,
} from '../../../application';
import { HttpExceptionFilter } from '../../filters';
import {
  CreateRoleRequest,
  UpdateRoleRequest,
  CreateCompanyRequest,
  UpdateCompanyRequest,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  UpdateUserRequest,
} from '../../requests';
import {
  UserResponse,
  RoleResponse,
  CompanyResponse,
  EmployeeResponse,
} from '../../responses';

@Controller('admin/public')
@UseFilters(HttpExceptionFilter)
export class AdminPublicController {
  constructor(
    // User use cases
    private readonly findUserUseCase: FindUserUseCase,
    private readonly updateUserUseCase: UpdateUserUseCase,
    private readonly deleteUserUseCase: DeleteUserUseCase,
    // Role use cases
    private readonly createRoleUseCase: CreateRoleUseCase,
    private readonly findRoleUseCase: FindRoleUseCase,
    private readonly updateRoleUseCase: UpdateRoleUseCase,
    private readonly deleteRoleUseCase: DeleteRoleUseCase,
    // Company use cases
    private readonly createCompanyUseCase: CreateCompanyUseCase,
    private readonly findCompanyUseCase: FindCompanyUseCase,
    private readonly updateCompanyUseCase: UpdateCompanyUseCase,
    private readonly deleteCompanyUseCase: DeleteCompanyUseCase,
    // Employee use cases
    private readonly createEmployeeUseCase: CreateEmployeeUseCase,
    private readonly findEmployeeUseCase: FindEmployeeUseCase,
    private readonly updateEmployeeUseCase: UpdateEmployeeUseCase,
    private readonly deleteEmployeeUseCase: DeleteEmployeeUseCase,
  ) {}

  // User endpoints
  @Get('users')
  async getAllUsers(): Promise<UserResponse[]> {
    const users = await this.findUserUseCase.findAll();
    return users.map((user) => new UserResponse(user));
  }

  @Get('users/:id')
  async getUserById(@Param('id') id: string): Promise<UserResponse> {
    const user = await this.findUserUseCase.findById(id);
    return new UserResponse(user);
  }

  @Put('users/:id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateUser(
    @Param('id') id: string,
    @Body() request: UpdateUserRequest,
  ): Promise<UserResponse> {
    const userDto = await this.updateUserUseCase.execute(id, {
      email: request.email,
      name: request.name,
      picture: request.picture,
    });

    return new UserResponse(userDto);
  }

  @Delete('users/:id')
  async deleteUser(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteUserUseCase.execute(id);
    return { message: 'User deleted successfully' };
  }

  // Role endpoints
  @Get('roles')
  async getAllRoles(): Promise<RoleResponse[]> {
    const roles = await this.findRoleUseCase.findAll();
    return roles.map((role) => new RoleResponse(role));
  }

  @Get('roles/:id')
  async getRoleById(@Param('id') id: string): Promise<RoleResponse> {
    const role = await this.findRoleUseCase.findById(id);
    return new RoleResponse(role);
  }

  @Post('roles')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createRole(@Body() request: CreateRoleRequest): Promise<RoleResponse> {
    const roleDto = await this.createRoleUseCase.execute({
      name: request.name,
      description: request.description,
      permissions: request.permissions,
    });

    return new RoleResponse(roleDto);
  }

  @Put('roles/:id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateRole(
    @Param('id') id: string,
    @Body() request: UpdateRoleRequest,
  ): Promise<RoleResponse> {
    const roleDto = await this.updateRoleUseCase.execute(id, {
      name: request.name,
      description: request.description,
      permissions: request.permissions,
    });

    return new RoleResponse(roleDto);
  }

  @Delete('roles/:id')
  async deleteRole(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteRoleUseCase.execute(id);
    return { message: 'Role deleted successfully' };
  }

  // Company endpoints
  @Get('companies')
  async getAllCompanies(): Promise<CompanyResponse[]> {
    const companies = await this.findCompanyUseCase.findAll();
    return companies.map((company) => new CompanyResponse(company));
  }

  @Get('companies/:id')
  async getCompanyById(@Param('id') id: string): Promise<CompanyResponse> {
    const company = await this.findCompanyUseCase.findById(id);
    return new CompanyResponse(company);
  }

  @Post('companies')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createCompany(
    @Body() request: CreateCompanyRequest,
  ): Promise<CompanyResponse> {
    const companyDto = await this.createCompanyUseCase.execute({
      name: request.name,
      description: request.description,
      website: request.website,
      industry: request.industry,
      size: request.size,
    });

    return new CompanyResponse(companyDto);
  }

  @Put('companies/:id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateCompany(
    @Param('id') id: string,
    @Body() request: UpdateCompanyRequest,
  ): Promise<CompanyResponse> {
    const companyDto = await this.updateCompanyUseCase.execute(id, {
      name: request.name,
      description: request.description,
      website: request.website,
      industry: request.industry,
      size: request.size,
    });

    return new CompanyResponse(companyDto);
  }

  @Delete('companies/:id')
  async deleteCompany(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteCompanyUseCase.execute(id);
    return { message: 'Company deleted successfully' };
  }

  // Employee endpoints
  @Get('employees')
  async getAllEmployees(): Promise<EmployeeResponse[]> {
    const employees = await this.findEmployeeUseCase.findAll();
    return employees.map((employee) => new EmployeeResponse(employee));
  }

  @Get('employees/:id')
  async getEmployeeById(@Param('id') id: string): Promise<EmployeeResponse> {
    const employee = await this.findEmployeeUseCase.findById(id);
    return new EmployeeResponse(employee);
  }

  @Get('employees/user/:userId')
  async getEmployeeByUserId(
    @Param('userId') userId: string,
  ): Promise<EmployeeResponse> {
    const employee = await this.findEmployeeUseCase.findByUserId(userId);
    return new EmployeeResponse(employee);
  }

  @Post('employees')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createEmployee(
    @Body() request: CreateEmployeeRequest,
  ): Promise<EmployeeResponse> {
    const employeeDto = await this.createEmployeeUseCase.execute({
      userId: request.userId,
      companyId: request.companyId,
      position: request.position,
      department: request.department,
      salary: request.salary,
      startDate: request.startDate,
      endDate: request.endDate,
      status: request.status,
    });

    return new EmployeeResponse(employeeDto);
  }

  @Put('employees/:id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateEmployee(
    @Param('id') id: string,
    @Body() request: UpdateEmployeeRequest,
  ): Promise<EmployeeResponse> {
    const employeeDto = await this.updateEmployeeUseCase.execute(id, {
      userId: request.userId,
      companyId: request.companyId,
      position: request.position,
      department: request.department,
      salary: request.salary,
      startDate: request.startDate,
      endDate: request.endDate,
      status: request.status,
    });

    return new EmployeeResponse(employeeDto);
  }

  @Delete('employees/:id')
  async deleteEmployee(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteEmployeeUseCase.execute(id);
    return { message: 'Employee deleted successfully' };
  }

  // Stats endpoint
  @Get('stats')
  async getStats(): Promise<{
    totalUsers: number;
    totalCompanies: number;
    totalEmployees: number;
    totalRoles: number;
  }> {
    const [users, companies, employees, roles] = await Promise.all([
      this.findUserUseCase.findAll(),
      this.findCompanyUseCase.findAll(),
      this.findEmployeeUseCase.findAll(),
      this.findRoleUseCase.findAll(),
    ]);

    return {
      totalUsers: users.length,
      totalCompanies: companies.length,
      totalEmployees: employees.length,
      totalRoles: roles.length,
    };
  }
}
