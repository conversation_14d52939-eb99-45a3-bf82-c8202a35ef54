import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateEmployeeUseCase,
  FindEmployeeUseCase,
  UpdateEmployeeUseCase,
  DeleteEmployeeUseCase,
} from '../../../application';
import { HttpExceptionFilter } from '../../filters';
import { RolesGuard } from '../../../infrastructure/auth/guards';
import { Roles } from '../../../infrastructure/auth/decorators';
import { CreateEmployeeRequest, UpdateEmployeeRequest } from '../../requests';
import { EmployeeResponse } from '../../responses';

@Controller('admin/employees')
@UseGuards(RolesGuard)
@UseFilters(HttpExceptionFilter)
export class AdminEmployeeController {
  constructor(
    private readonly createEmployeeUseCase: CreateEmployeeUseCase,
    private readonly findEmployeeUseCase: FindEmployeeUseCase,
    private readonly updateEmployeeUseCase: UpdateEmployeeUseCase,
    private readonly deleteEmployeeUseCase: DeleteEmployeeUseCase,
  ) {}

  @Post()
  @Roles('admin', 'employer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createEmployee(
    @Body() request: CreateEmployeeRequest,
  ): Promise<EmployeeResponse> {
    const employeeDto = await this.createEmployeeUseCase.execute({
      userId: request.userId,
      companyId: request.companyId,
      position: request.position,
      department: request.department,
      salary: request.salary,
      startDate: request.startDate,
      status: request.status,
    });

    return new EmployeeResponse(employeeDto);
  }

  @Get()
  @Roles('admin')
  async getAllEmployees(
    @Query('companyId') companyId?: string,
  ): Promise<EmployeeResponse[]> {
    const employees = companyId
      ? await this.findEmployeeUseCase.findByCompanyId(companyId)
      : await this.findEmployeeUseCase.findAll();

    return employees.map((employee) => new EmployeeResponse(employee));
  }

  @Get(':id')
  @Roles('admin', 'employer')
  async getEmployeeById(@Param('id') id: string): Promise<EmployeeResponse> {
    const employee = await this.findEmployeeUseCase.findById(id);
    return new EmployeeResponse(employee);
  }

  @Get('user/:userId')
  @Roles('admin', 'employer', 'employee')
  async getEmployeeByUserId(
    @Param('userId') userId: string,
  ): Promise<EmployeeResponse> {
    const employee = await this.findEmployeeUseCase.findByUserId(userId);
    return new EmployeeResponse(employee);
  }

  @Put(':id')
  @Roles('admin', 'employer')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateEmployee(
    @Param('id') id: string,
    @Body() request: UpdateEmployeeRequest,
  ): Promise<EmployeeResponse> {
    const employeeDto = await this.updateEmployeeUseCase.execute(id, {
      position: request.position,
      department: request.department,
      salary: request.salary,
      startDate: request.startDate,
      endDate: request.endDate,
      status: request.status,
    });

    return new EmployeeResponse(employeeDto);
  }

  @Delete(':id')
  @Roles('admin')
  async deleteEmployee(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteEmployeeUseCase.execute(id);
    return { message: 'Employee deleted successfully' };
  }
}
