import { Controller, Get, UseGuards, UseFilters } from '@nestjs/common';
import { HttpExceptionFilter } from '../../filters';
import { RolesGuard } from '../../../infrastructure/auth/guards';
import { Roles } from '../../../infrastructure/auth/decorators';

@Controller('admin/test')
@UseGuards(RolesGuard)
@UseFilters(HttpExceptionFilter)
export class AdminTestController {
  @Get()
  @Roles('admin')
  async testEndpoint(): Promise<{ message: string; timestamp: string }> {
    return {
      message: 'Admin API is working!',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('stats')
  @Roles('admin')
  async getTestStats(): Promise<{
    totalUsers: number;
    totalCompanies: number;
    totalEmployees: number;
    totalRoles: number;
  }> {
    // Return mock stats for testing
    return {
      totalUsers: 3,
      totalCompanies: 2,
      totalEmployees: 5,
      totalRoles: 4,
    };
  }
}
