import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateCompanyUseCase,
  FindCompanyUseCase,
  UpdateCompanyUseCase,
  DeleteCompanyUseCase,
} from '../../../application';
import { HttpExceptionFilter } from '../../filters';
import { RolesGuard } from '../../../infrastructure/auth/guards';
import { Roles } from '../../../infrastructure/auth/decorators';
import { CreateCompanyRequest, UpdateCompanyRequest } from '../../requests';
import { CompanyResponse } from '../../responses';

@Controller('admin/companies')
@UseGuards(RolesGuard)
@UseFilters(HttpExceptionFilter)
export class AdminCompanyController {
  constructor(
    private readonly createCompanyUseCase: CreateCompanyUseCase,
    private readonly findCompanyUseCase: FindCompanyUseCase,
    private readonly updateCompanyUseCase: UpdateCompanyUseCase,
    private readonly deleteCompanyUseCase: DeleteCompanyUseCase,
  ) {}

  @Post()
  @Roles('admin')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createCompany(
    @Body() request: CreateCompanyRequest,
  ): Promise<CompanyResponse> {
    const companyDto = await this.createCompanyUseCase.execute({
      name: request.name,
      description: request.description,
      website: request.website,
      industry: request.industry,
      size: request.size,
    });

    return new CompanyResponse(companyDto);
  }

  @Get()
  @Roles('admin')
  async getAllCompanies(): Promise<CompanyResponse[]> {
    const companies = await this.findCompanyUseCase.findAll();
    return companies.map((company) => new CompanyResponse(company));
  }

  @Get(':id')
  @Roles('admin')
  async getCompanyById(@Param('id') id: string): Promise<CompanyResponse> {
    const company = await this.findCompanyUseCase.findById(id);
    return new CompanyResponse(company);
  }

  @Put(':id')
  @Roles('admin')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateCompany(
    @Param('id') id: string,
    @Body() request: UpdateCompanyRequest,
  ): Promise<CompanyResponse> {
    const companyDto = await this.updateCompanyUseCase.execute(id, {
      name: request.name,
      description: request.description,
      website: request.website,
      industry: request.industry,
      size: request.size,
    });

    return new CompanyResponse(companyDto);
  }

  @Delete(':id')
  @Roles('admin')
  async deleteCompany(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteCompanyUseCase.execute(id);
    return { message: 'Company deleted successfully' };
  }
}
