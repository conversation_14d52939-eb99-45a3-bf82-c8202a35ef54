import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateRoleUseCase,
  FindRoleUseCase,
  UpdateRoleUseCase,
  DeleteRoleUseCase,
} from '../../../application';
import { HttpExceptionFilter } from '../../filters';
import { RolesGuard } from '../../../infrastructure/auth/guards';
import { Roles } from '../../../infrastructure/auth/decorators';
import { CreateRoleRequest, UpdateRoleRequest } from '../../requests';
import { RoleResponse } from '../../responses';

@Controller('admin/roles')
@UseGuards(RolesGuard)
@UseFilters(HttpExceptionFilter)
export class AdminRoleController {
  constructor(
    private readonly createRoleUseCase: CreateRoleUseCase,
    private readonly findRoleUseCase: FindRoleUseCase,
    private readonly updateRoleUseCase: UpdateRoleUseCase,
    private readonly deleteRoleUseCase: DeleteRoleUseCase,
  ) {}

  @Post()
  @Roles('admin')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createRole(@Body() request: CreateRoleRequest): Promise<RoleResponse> {
    const roleDto = await this.createRoleUseCase.execute({
      name: request.name,
      description: request.description,
      permissions: request.permissions,
    });

    return new RoleResponse(roleDto);
  }

  @Get()
  @Roles('admin')
  async getAllRoles(): Promise<RoleResponse[]> {
    const roles = await this.findRoleUseCase.findAll();
    return roles.map((role) => new RoleResponse(role));
  }

  @Get(':id')
  @Roles('admin')
  async getRoleById(@Param('id') id: string): Promise<RoleResponse> {
    const role = await this.findRoleUseCase.findById(id);
    return new RoleResponse(role);
  }

  @Put(':id')
  @Roles('admin')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateRole(
    @Param('id') id: string,
    @Body() request: UpdateRoleRequest,
  ): Promise<RoleResponse> {
    const roleDto = await this.updateRoleUseCase.execute(id, {
      description: request.description,
      permissions: request.permissions,
    });

    return new RoleResponse(roleDto);
  }

  @Delete(':id')
  @Roles('admin')
  async deleteRole(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteRoleUseCase.execute(id);
    return { message: 'Role deleted successfully' };
  }
}
