import {
  Controller,
  Get,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  FindUserUseCase,
  UpdateUserUseCase,
  DeleteUserUseCase,
} from '../../../application';
import { HttpExceptionFilter } from '../../filters';
import { RolesGuard } from '../../../infrastructure/auth/guards';
import { Roles } from '../../../infrastructure/auth/decorators';
import { UpdateUserRequest } from '../../requests';
import { UserResponse } from '../../responses';

@Controller('admin/users')
@UseGuards(RolesGuard)
@UseFilters(HttpExceptionFilter)
export class AdminUserController {
  constructor(
    private readonly findUserUseCase: FindUserUseCase,
    private readonly updateUserUseCase: UpdateUserUseCase,
    private readonly deleteUserUseCase: DeleteUserUseCase,
  ) {}

  @Get()
  @Roles('admin')
  async getAllUsers(): Promise<UserResponse[]> {
    const users = await this.findUserUseCase.findAll();
    return users.map((user) => new UserResponse(user));
  }

  @Get(':id')
  @Roles('admin')
  async getUserById(@Param('id') id: string): Promise<UserResponse> {
    const user = await this.findUserUseCase.findById(id);
    return new UserResponse(user);
  }

  @Put(':id')
  @Roles('admin')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateUser(
    @Param('id') id: string,
    @Body() request: UpdateUserRequest,
  ): Promise<UserResponse> {
    const userDto = await this.updateUserUseCase.execute(id, {
      email: request.email,
      name: request.name,
    });

    return new UserResponse(userDto);
  }

  @Delete(':id')
  @Roles('admin')
  async deleteUser(@Param('id') id: string): Promise<{ message: string }> {
    await this.deleteUserUseCase.execute(id);
    return { message: 'User deleted successfully' };
  }
}
