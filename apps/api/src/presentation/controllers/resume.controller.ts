import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Body,
  UseFilters,
  ValidationPipe,
  UsePipes,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ExtractResumeDataUseCase } from '../../application/use-cases/resume/extract-resume-data.use-case';
import { SubmitResumeUseCase } from '../../application/use-cases/resume/submit-resume.use-case';
import { SubmitResumeRequest, ResumeDataResponse, SubmitResumeResponse } from '../dtos/resume.dto';
import { HttpExceptionFilter } from '../filters';

@Controller('resume')
@UseFilters(HttpExceptionFilter)
export class ResumeController {
  constructor(
    private readonly extractResumeDataUseCase: ExtractResumeDataUseCase,
    private readonly submitResumeUseCase: SubmitResumeUseCase,
  ) {}

  @Post('extract')
  @UseInterceptors(
    FileInterceptor('resume', {
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
      fileFilter: (req, file, callback) => {
        const allowedMimeTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];
        
        if (allowedMimeTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new BadRequestException('Only PDF, DOC, and DOCX files are allowed'), false);
        }
      },
    }),
  )
  async extractResumeData(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ResumeDataResponse> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const extractedData = await this.extractResumeDataUseCase.execute(file);
    return new ResumeDataResponse(extractedData);
  }

  @Post('submit')
  @UseInterceptors(
    FileInterceptor('resume', {
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
      fileFilter: (req, file, callback) => {
        const allowedMimeTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];
        
        if (allowedMimeTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new BadRequestException('Only PDF, DOC, and DOCX files are allowed'), false);
        }
      },
    }),
  )
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async submitResume(
    @UploadedFile() file: Express.Multer.File,
    @Body() request: SubmitResumeRequest,
  ): Promise<SubmitResumeResponse> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const result = await this.submitResumeUseCase.execute({
      ...request,
      fileName: file.originalname,
      fileSize: file.size,
      fileType: file.mimetype,
    });

    return new SubmitResumeResponse(result.id, result.message, result.submittedAt);
  }
}
