import {
  Controller,
  Post,
  Body,
  UseFilters,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsArray,
  ValidateNested,
  IsIn,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SendMessageUseCase } from '../../application/use-cases/chat/send-message.use-case';
import { HttpExceptionFilter } from '../filters';

class ConversationMessage {
  @IsIn(['user', 'assistant'])
  role: 'user' | 'assistant';

  @IsString()
  @IsNotEmpty()
  content: string;
}

export class SendMessageRequest {
  @IsString()
  @IsNotEmpty()
  message: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ConversationMessage)
  conversationHistory?: ConversationMessage[];
}

export class SendMessageResponse {
  message: string;
  conversationId?: string;
}

@Controller('chat')
@UseFilters(HttpExceptionFilter)
export class ChatController {
  constructor(private readonly sendMessageUseCase: SendMessageUseCase) {}

  @Post('message')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async sendMessage(
    @Body() request: SendMessageRequest,
  ): Promise<SendMessageResponse> {
    const response = await this.sendMessageUseCase.execute({
      message: request.message,
      conversationHistory: request.conversationHistory,
    });

    return {
      message: response.message,
      conversationId: response.conversationId,
    };
  }
}
