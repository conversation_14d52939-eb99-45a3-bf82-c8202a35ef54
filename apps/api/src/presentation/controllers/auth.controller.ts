import {
  <PERSON>,
  Get,
  Post,
  Patch,
  UseGuards,
  Req,
  Res,
  Body,
  Param,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request, Response } from 'express';
import {
  OAuthSignupUseCase,
  OAuthSigninUseCase,
  UpdateUserUseCase,
} from '../../application';
import { HttpExceptionFilter } from '../filters';

interface GoogleUser {
  providerId: string;
  email: string;
  name: string;
  picture?: string;
  accessToken: string;
  refreshToken: string;
}

interface LinkedInUser {
  providerId: string;
  email: string;
  name: string;
  picture?: string;
  accessToken: string;
  refreshToken: string;
}

@Controller('auth')
@UseFilters(HttpExceptionFilter)
export class AuthController {
  constructor(
    private readonly oauthSignupUseCase: OAuthSignupUseCase,
    private readonly oauthSigninUseCase: OAuthSigninUseCase,
    private readonly updateUserUseCase: UpdateUserUseCase,
  ) {}

  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {
    // This route initiates the Google OAuth flow
    // The actual redirect is handled by Passport
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req: Request, @Res() res: Response) {
    const user = req.user as GoogleUser;

    try {
      const existingUser = await this.oauthSigninUseCase.execute({
        provider: 'google',
        providerId: user.providerId,
      });

      const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
      const redirectUrl = `${frontendUrl}/en/auth/callback?success=true&user=${encodeURIComponent(JSON.stringify(existingUser))}`;

      return res.redirect(redirectUrl);
    } catch (error) {
      try {
        const newUser = await this.oauthSignupUseCase.execute({
          email: user.email,
          name: user.name,
          provider: 'google',
          providerId: user.providerId,
          picture: user.picture,
        });

        const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
        const redirectUrl = `${frontendUrl}/en/auth/callback?success=true&user=${encodeURIComponent(JSON.stringify(newUser))}`;

        return res.redirect(redirectUrl);
      } catch (signupError) {
        const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
        const redirectUrl = `${frontendUrl}/en/auth/callback?success=false&error=${encodeURIComponent(signupError.message)}`;

        return res.redirect(redirectUrl);
      }
    }
  }

  @Post('google/signup')
  async googleSignup(
    @Body()
    body: {
      email: string;
      name: string;
      providerId: string;
      picture?: string;
    },
  ) {
    try {
      console.log('OAuth Signup Request:', body);

      const newUser = await this.oauthSignupUseCase.execute({
        email: body.email,
        name: body.name,
        provider: 'google',
        providerId: body.providerId,
        picture: body.picture,
      });

      return {
        success: true,
        user: newUser,
      };
    } catch (error) {
      console.error('OAuth Signup Error:', error);
      throw error;
    }
  }

  @Post('google/signin')
  async googleSignin(@Body() body: { providerId: string }) {
    try {
      console.log('OAuth Signin Request:', body);

      const existingUser = await this.oauthSigninUseCase.execute({
        provider: 'google',
        providerId: body.providerId,
      });

      return {
        success: true,
        user: existingUser,
      };
    } catch (error) {
      console.error('OAuth Signin Error:', error);
      throw error;
    }
  }

  // LinkedIn OAuth endpoints
  @Get('linkedin')
  @UseGuards(AuthGuard('linkedin'))
  async linkedinAuth() {
    // This route initiates the LinkedIn OAuth flow
    // The actual redirect is handled by Passport
  }

  @Get('linkedin/callback')
  @UseGuards(AuthGuard('linkedin'))
  async linkedinAuthRedirect(@Req() req: Request, @Res() res: Response) {
    console.log('LinkedIn callback received');
    const user = req.user as LinkedInUser;
    console.log('LinkedIn user data:', user);

    if (!user) {
      console.error('No user data received from LinkedIn');
      const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
      const redirectUrl = `${frontendUrl}/en/auth/callback?success=false&error=${encodeURIComponent('No user data received from LinkedIn')}`;
      return res.redirect(redirectUrl);
    }

    if (!user.email) {
      console.error('No email received from LinkedIn');
      const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
      const redirectUrl = `${frontendUrl}/en/auth/callback?success=false&error=${encodeURIComponent('Email is required but not provided by LinkedIn')}`;
      return res.redirect(redirectUrl);
    }

    try {
      // First try to sign in (user already exists)
      const existingUser = await this.oauthSigninUseCase.execute({
        provider: 'linkedin',
        providerId: user.providerId,
      });

      console.log('LinkedIn user signed in successfully:', existingUser);
      // Redirect to frontend with success and user data
      const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
      const redirectUrl = `${frontendUrl}/en/auth/callback?success=true&user=${encodeURIComponent(JSON.stringify(existingUser))}`;

      return res.redirect(redirectUrl);
    } catch (error) {
      console.log('LinkedIn user not found, attempting signup:', error.message);
      // User doesn't exist, try to sign up
      try {
        const newUser = await this.oauthSignupUseCase.execute({
          email: user.email,
          name: user.name,
          provider: 'linkedin',
          providerId: user.providerId,
          picture: user.picture,
        });

        console.log('LinkedIn user signed up successfully:', newUser);
        // Redirect to frontend with success and user data
        const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
        const redirectUrl = `${frontendUrl}/en/auth/callback?success=true&user=${encodeURIComponent(JSON.stringify(newUser))}`;

        return res.redirect(redirectUrl);
      } catch (signupError) {
        console.error('LinkedIn signup failed:', signupError);
        // Both signin and signup failed
        const frontendUrl = process.env.FRONTEND_URL ?? 'http://localhost:3000';
        const redirectUrl = `${frontendUrl}/en/auth/callback?success=false&error=${encodeURIComponent(signupError.message)}`;

        return res.redirect(redirectUrl);
      }
    }
  }

  @Post('linkedin/signup')
  async linkedinSignup(
    @Body()
    body: {
      email: string;
      name: string;
      providerId: string;
      picture?: string;
    },
  ) {
    try {
      console.log('LinkedIn OAuth Signup Request:', body);

      const newUser = await this.oauthSignupUseCase.execute({
        email: body.email,
        name: body.name,
        provider: 'linkedin',
        providerId: body.providerId,
        picture: body.picture,
      });

      return {
        success: true,
        user: newUser,
      };
    } catch (error) {
      console.error('LinkedIn OAuth Signup Error:', error);
      throw error;
    }
  }

  @Post('linkedin/signin')
  async linkedinSignin(@Body() body: { providerId: string }) {
    try {
      console.log('LinkedIn OAuth Signin Request:', body);

      const existingUser = await this.oauthSigninUseCase.execute({
        provider: 'linkedin',
        providerId: body.providerId,
      });

      return {
        success: true,
        user: existingUser,
      };
    } catch (error) {
      console.error('LinkedIn OAuth Signin Error:', error);
      throw error;
    }
  }

  @Patch('complete-profile/:userId')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async completeProfile(
    @Param('userId') userId: string,
    @Body()
    body: {
      email?: string;
      name?: string;
      phoneNumber?: string;
      birthDate?: string;
      jobPosition?: string;
      address?: string;
    },
  ) {
    try {
      console.log('Profile completion request:', { userId, body });

      const updatedUser = await this.updateUserUseCase.execute(userId, {
        email: body.email,
        name: body.name,
        phoneNumber: body.phoneNumber,
        birthDate: body.birthDate ? new Date(body.birthDate) : undefined,
        jobPosition: body.jobPosition,
        address: body.address,
      });

      return {
        success: true,
        user: updatedUser,
        message: 'Profile completed successfully',
      };
    } catch (error) {
      console.error('Profile completion error:', error);
      throw error;
    }
  }
}
