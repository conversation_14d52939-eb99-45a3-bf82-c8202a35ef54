import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseFilters,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  CreateUserUseCase,
  FindUserUseCase,
  UpdateUserUseCase,
  DeleteUserUseCase,
} from '../../application';
import { CreateUserRequest, UpdateUserRequest, UserResponse } from '../dtos';
import { HttpExceptionFilter } from '../filters';

@Controller('users')
@UseFilters(HttpExceptionFilter)
export class UserController {
  constructor(
    private readonly createUserUseCase: CreateUserUseCase,
    private readonly findUserUseCase: FindUserUseCase,
    private readonly updateUserUseCase: UpdateUserUseCase,
    private readonly deleteUserUseCase: DeleteUserUseCase,
  ) {}

  @Post()
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async createUser(@Body() request: CreateUserRequest): Promise<UserResponse> {
    const userDto = await this.createUserUseCase.execute({
      email: request.email,
      name: request.name,
    });

    return new UserResponse(userDto);
  }

  @Get()
  async findAllUsers(): Promise<UserResponse[]> {
    const users = await this.findUserUseCase.findAll();
    return users.map(user => new UserResponse(user));
  }

  @Get(':id')
  async findUserById(@Param('id') id: string): Promise<UserResponse> {
    const user = await this.findUserUseCase.findById(id);
    return new UserResponse(user);
  }

  @Put(':id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateUser(
    @Param('id') id: string,
    @Body() request: UpdateUserRequest,
  ): Promise<UserResponse> {
    const userDto = await this.updateUserUseCase.execute(id, {
      email: request.email,
      name: request.name,
    });

    return new UserResponse(userDto);
  }

  @Delete(':id')
  async deleteUser(@Param('id') id: string): Promise<void> {
    await this.deleteUserUseCase.execute(id);
  }
}
