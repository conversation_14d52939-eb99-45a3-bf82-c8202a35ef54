import { IsString, IsEmail, IsOptional, IsNotEmpty } from 'class-validator';

export class SubmitResumeRequest {
  @IsString()
  @IsNotEmpty()
  fullName: string;

  @IsEmail()
  email: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsNotEmpty()
  location: string;

  @IsString()
  @IsNotEmpty()
  jobTitle: string;

  @IsString()
  @IsNotEmpty()
  experience: string;

  @IsString()
  @IsNotEmpty()
  skills: string;

  @IsString()
  @IsNotEmpty()
  education: string;

  @IsString()
  @IsOptional()
  summary?: string;
}

export class ResumeDataResponse {
  fullName?: string;
  email?: string;
  phone?: string;
  location?: string;
  jobTitle?: string;
  experience?: string;
  skills?: string;
  education?: string;
  summary?: string;

  constructor(data: Partial<ResumeDataResponse>) {
    Object.assign(this, data);
  }
}

export class SubmitResumeResponse {
  id: string;
  message: string;
  submittedAt: Date;

  constructor(id: string, message: string, submittedAt: Date) {
    this.id = id;
    this.message = message;
    this.submittedAt = submittedAt;
  }
}
