import { Injectable } from '@nestjs/common';
import { Employee, EmployeeRepository } from '../../domain';
import { EmployeeId, UserId, CompanyId } from '../../domain/value-objects';
import { DatabaseService } from '../../database/database.service';

@Injectable()
export class PrismaEmployeeRepository implements EmployeeRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  async save(employee: Employee): Promise<Employee> {
    const employeeData = employee.toPlainObject();

    const savedEmployee = await this.databaseService.employee.upsert({
      where: { id: employeeData.id },
      update: {
        position: employeeData.position,
        department: employeeData.department,
        salary: employeeData.salary,
        startDate: employeeData.startDate,
        endDate: employeeData.endDate,
        status: employeeData.status,
        updatedAt: employeeData.updatedAt,
      },
      create: {
        id: employeeData.id,
        userId: employeeData.userId,
        companyId: employeeData.companyId,
        position: employeeData.position,
        department: employeeData.department,
        salary: employeeData.salary,
        startDate: employeeData.startDate,
        endDate: employeeData.endDate,
        status: employeeData.status,
        createdAt: employeeData.createdAt,
        updatedAt: employeeData.updatedAt,
      },
    });

    return Employee.fromPersistence(savedEmployee);
  }

  async findById(id: EmployeeId): Promise<Employee | null> {
    const employee = await this.databaseService.employee.findUnique({
      where: { id: id.value },
    });

    if (!employee) {
      return null;
    }

    return Employee.fromPersistence(employee);
  }

  async findByUserId(userId: UserId): Promise<Employee | null> {
    const employee = await this.databaseService.employee.findUnique({
      where: { userId: userId.value },
    });

    if (!employee) {
      return null;
    }

    return Employee.fromPersistence(employee);
  }

  async findByCompanyId(companyId: CompanyId): Promise<Employee[]> {
    const employees = await this.databaseService.employee.findMany({
      where: { companyId: companyId.value },
      orderBy: { createdAt: 'desc' },
    });

    return employees.map((employee) => Employee.fromPersistence(employee));
  }

  async findAll(): Promise<Employee[]> {
    const employees = await this.databaseService.employee.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return employees.map((employee) => Employee.fromPersistence(employee));
  }

  async delete(id: EmployeeId): Promise<void> {
    await this.databaseService.employee.delete({
      where: { id: id.value },
    });
  }

  async exists(id: EmployeeId): Promise<boolean> {
    const count = await this.databaseService.employee.count({
      where: { id: id.value },
    });

    return count > 0;
  }

  async existsByUserId(userId: UserId): Promise<boolean> {
    const count = await this.databaseService.employee.count({
      where: { userId: userId.value },
    });

    return count > 0;
  }
}
