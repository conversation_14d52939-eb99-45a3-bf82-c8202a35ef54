import { Injectable } from '@nestjs/common';
import { Role, RoleRepository } from '../../domain';
import { RoleId, RoleName } from '../../domain/value-objects';
import { DatabaseService } from '../../database/database.service';

@Injectable()
export class PrismaRoleRepository implements RoleRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  async save(role: Role): Promise<Role> {
    const roleData = role.toPlainObject();

    const savedRole = await this.databaseService.role.upsert({
      where: { id: roleData.id },
      update: {
        name: roleData.name,
        description: roleData.description,
        permissions: roleData.permissions,
        updatedAt: roleData.updatedAt,
      },
      create: {
        id: roleData.id,
        name: roleData.name,
        description: roleData.description,
        permissions: roleData.permissions,
        createdAt: roleData.createdAt,
        updatedAt: roleData.updatedAt,
      },
    });

    return Role.fromPersistence(savedRole);
  }

  async findById(id: RoleId): Promise<Role | null> {
    const role = await this.databaseService.role.findUnique({
      where: { id: id.value },
    });

    if (!role) {
      return null;
    }

    return Role.fromPersistence(role);
  }

  async findByName(name: RoleName): Promise<Role | null> {
    const role = await this.databaseService.role.findUnique({
      where: { name: name.value },
    });

    if (!role) {
      return null;
    }

    return Role.fromPersistence(role);
  }

  async findAll(): Promise<Role[]> {
    const roles = await this.databaseService.role.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return roles.map((role) => Role.fromPersistence(role));
  }

  async delete(id: RoleId): Promise<void> {
    await this.databaseService.role.delete({
      where: { id: id.value },
    });
  }

  async exists(id: RoleId): Promise<boolean> {
    const count = await this.databaseService.role.count({
      where: { id: id.value },
    });

    return count > 0;
  }

  async existsByName(name: RoleName): Promise<boolean> {
    const count = await this.databaseService.role.count({
      where: { name: name.value },
    });

    return count > 0;
  }
}
