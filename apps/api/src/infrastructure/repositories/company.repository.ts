import { Injectable } from '@nestjs/common';
import { Company, CompanyRepository } from '../../domain';
import { CompanyId } from '../../domain/value-objects';
import { DatabaseService } from '../../database/database.service';

@Injectable()
export class PrismaCompanyRepository implements CompanyRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  async save(company: Company): Promise<Company> {
    const companyData = company.toPlainObject();

    const savedCompany = await this.databaseService.company.upsert({
      where: { id: companyData.id },
      update: {
        name: companyData.name,
        description: companyData.description,
        website: companyData.website,
        industry: companyData.industry,
        size: companyData.size,
        updatedAt: companyData.updatedAt,
      },
      create: {
        id: companyData.id,
        name: companyData.name,
        description: companyData.description,
        website: companyData.website,
        industry: companyData.industry,
        size: companyData.size,
        createdAt: companyData.createdAt,
        updatedAt: companyData.updatedAt,
      },
    });

    return Company.fromPersistence(savedCompany);
  }

  async findById(id: CompanyId): Promise<Company | null> {
    const company = await this.databaseService.company.findUnique({
      where: { id: id.value },
    });

    if (!company) {
      return null;
    }

    return Company.fromPersistence(company);
  }

  async findByName(name: string): Promise<Company | null> {
    const company = await this.databaseService.company.findFirst({
      where: { name: { equals: name, mode: 'insensitive' } },
    });

    if (!company) {
      return null;
    }

    return Company.fromPersistence(company);
  }

  async findAll(): Promise<Company[]> {
    const companies = await this.databaseService.company.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return companies.map((company) => Company.fromPersistence(company));
  }

  async delete(id: CompanyId): Promise<void> {
    await this.databaseService.company.delete({
      where: { id: id.value },
    });
  }

  async exists(id: CompanyId): Promise<boolean> {
    const count = await this.databaseService.company.count({
      where: { id: id.value },
    });

    return count > 0;
  }

  async existsByName(name: string): Promise<boolean> {
    const count = await this.databaseService.company.count({
      where: { name: { equals: name, mode: 'insensitive' } },
    });

    return count > 0;
  }
}
