import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-linkedin-oauth2';

export interface LinkedInProfile {
  id: string;
  emails: Array<{ value: string }>;
  name: {
    givenName: string;
    familyName: string;
  };
  photos: Array<{ value: string }>;
  // LinkedIn API v2 structure
  sub?: string;
  email?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
}

@Injectable()
export class LinkedInStrategy extends PassportStrategy(Strategy, 'linkedin') {
  constructor(private readonly configService: ConfigService) {
    super({
      clientID: configService.get<string>('LINKEDIN_CLIENT_ID')!,
      clientSecret: configService.get<string>('LINKEDIN_CLIENT_SECRET')!,
      callbackURL: configService.get<string>('LINKEDIN_CALLBACK_URL')!,
      scope: ['openid', 'profile', 'email'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: LinkedInProfile,
    done: (error: any, user?: any) => void,
  ): Promise<any> {
    const id = profile.id || profile.sub;
    const email = profile.email || profile.emails?.[0]?.value;
    const givenName = profile.given_name || profile.name?.givenName;
    const familyName = profile.family_name || profile.name?.familyName;
    const picture = profile.picture || profile.photos?.[0]?.value;

    const user = {
      providerId: id,
      email: email,
      name: `${givenName || ''} ${familyName || ''}`.trim(),
      picture: picture,
      accessToken,
      refreshToken,
    };

    done(null, user);
  }
}
