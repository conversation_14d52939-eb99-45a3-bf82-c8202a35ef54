-- This file runs when the PostgreSQL container starts for the first time
-- It ensures the database and user are properly set up

-- Create the database if it doesn't exist (this is handled by POSTGRES_DB env var)
-- Create additional schemas or initial setup if needed

-- Grant all privileges to the user (this is handled by <PERSON><PERSON>, but keeping for reference)
-- GRANT ALL PRIVILEGES ON DATABASE checku_db TO checku_user;

-- You can add any additional initialization SQL here
-- For example, extensions:
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pgcrypto";

SELECT 'Database initialization completed' as status;
