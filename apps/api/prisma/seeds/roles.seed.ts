import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedRoles() {
  console.log('Seeding roles...');

  // Admin role with all permissions
  await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'System administrator with full access',
      permissions: [
        'user:read', 'user:write', 'user:delete',
        'role:read', 'role:write', 'role:delete',
        'company:read', 'company:write', 'company:delete',
        'employee:read', 'employee:write', 'employee:delete',
        'admin:access', 'reports:view'
      ],
    },
  });

  // Employer role
  await prisma.role.upsert({
    where: { name: 'employer' },
    update: {},
    create: {
      name: 'employer',
      description: 'Company employer with employee management access',
      permissions: [
        'employee:read', 'employee:write',
        'company:read', 'company:write',
        'reports:view'
      ],
    },
  });

  // Employee role
  await prisma.role.upsert({
    where: { name: 'employee' },
    update: {},
    create: {
      name: 'employee',
      description: 'Company employee with limited access',
      permissions: [
        'user:read'
      ],
    },
  });

  // Guest role
  await prisma.role.upsert({
    where: { name: 'guest' },
    update: {},
    create: {
      name: 'guest',
      description: 'Guest user with minimal access',
      permissions: [],
    },
  });

  console.log('Roles seeded successfully');
}
