// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Role model for RBAC
model Role {
  id          String   @id @default(cuid())
  name        String   @unique // 'admin', 'employer', 'employee', 'guest'
  description String?
  permissions Json?    // Store permissions as JSON array

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userRoles UserRole[]

  @@map("roles")
}

// Junction table for User-Role many-to-many relationship
model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String

  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// Company/Employer model
model Company {
  id          String  @id @default(cuid())
  name        String
  description String?
  website     String?
  industry    String?
  size        String? // 'startup', 'small', 'medium', 'large', 'enterprise'

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  employees Employee[]
  employers CompanyEmployer[]

  @@map("companies")
}

// Junction table for Company-Employer relationship
model CompanyEmployer {
  id        String @id @default(cuid())
  companyId String
  userId    String
  position  String? // 'owner', 'hr_manager', 'recruiter', etc.

  createdAt DateTime @default(now())

  // Relations
  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([companyId, userId])
  @@map("company_employers")
}

// Employee model
model Employee {
  id         String  @id @default(cuid())
  userId     String  @unique
  companyId  String
  position   String?
  department String?
  salary     Decimal?
  startDate  DateTime?
  endDate    DateTime?
  status     String  @default("active") // 'active', 'inactive', 'terminated'

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  company Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  @@map("employees")
}

// User model with OAuth support and role relationships
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?

  // OAuth fields
  provider     String?  // 'google', 'local', etc.
  providerId   String?  // OAuth provider's user ID
  picture      String?  // Profile picture URL

  // Profile completion fields
  phoneNumber  String?
  birthDate    DateTime?
  jobPosition  String?
  address      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userRoles        UserRole[]
  employee         Employee?
  companyEmployers CompanyEmployer[]

  @@unique([provider, providerId])
  @@map("users")
}
