# Docker PostgreSQL Setup

This setup provides a PostgreSQL database using Docker for the CheckU application.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Ports 5432 and 5050 available on your machine

## Quick Start

### 1. Start the Database

```bash
# Start PostgreSQL container
docker-compose up -d postgres

# Or start both PostgreSQL and pgAdmin
docker-compose up -d
```

### 2. Verify Database is Running

```bash
# Check container status
docker-compose ps

# Check database logs
docker-compose logs postgres

# Test database connection
docker-compose exec postgres psql -U checku_user -d checku_db -c "SELECT version();"
```

### 3. Run Database Migrations

```bash
cd apps/api
pnpm db:migrate
```

### 4. Seed the Database (Optional)

```bash
cd apps/api
pnpm db:seed
```

## Services

### PostgreSQL Database
- **Host**: localhost
- **Port**: 5432
- **Database**: checku_db
- **Username**: checku_user
- **Password**: checku_password
- **Connection URL**: `postgresql://checku_user:checku_password@localhost:5432/checku_db?schema=public`

### pgAdmin (Database Management UI)
- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin123

To connect to the database in pgAdmin:
1. Open http://localhost:5050
2. Login with the credentials above
3. Add a new server with these settings:
   - **Name**: CheckU Local
   - **Host**: postgres (container name)
   - **Port**: 5432
   - **Database**: checku_db
   - **Username**: checku_user
   - **Password**: checku_password

## Useful Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Stop and remove volumes (⚠️ This will delete all data)
docker-compose down -v

# View logs
docker-compose logs postgres
docker-compose logs pgadmin

# Access PostgreSQL shell
docker-compose exec postgres psql -U checku_user -d checku_db

# Backup database
docker-compose exec postgres pg_dump -U checku_user checku_db > backup.sql

# Restore database
docker-compose exec -T postgres psql -U checku_user -d checku_db < backup.sql

# Reset database (stop, remove volumes, start fresh)
docker-compose down -v && docker-compose up -d postgres
```

## Environment Variables

The database configuration matches your `.env` file:

```env
DATABASE_URL="postgresql://checku_user:checku_password@localhost:5432/checku_db?schema=public"
```

## Troubleshooting

### Port Already in Use
If port 5432 is already in use, you can change it in `docker-compose.yml`:
```yaml
ports:
  - "5433:5432"  # Use port 5433 instead
```
Then update your `DATABASE_URL` accordingly.

### Container Won't Start
```bash
# Check what's using the port
lsof -i :5432

# View detailed logs
docker-compose logs postgres

# Remove and recreate containers
docker-compose down
docker-compose up -d
```

### Data Persistence
Database data is stored in a Docker volume named `postgres_data`. This persists between container restarts. To completely reset:
```bash
docker-compose down -v
docker volume rm checku_postgres_data
docker-compose up -d
```
