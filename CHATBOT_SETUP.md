# Floating Chatbot Setup

This document explains how to set up and use the floating chatbot feature that has been added to your Checku application.

## Overview

The chatbot is a floating widget that appears in the bottom-left corner of every page. It uses OpenAI's GPT-3.5-turbo model to provide intelligent responses to user queries.

## Architecture

### Frontend Components (Atomic Design)

- **Atoms:**
  - `ChatMessage` - Individual message component
  - `ChatInput` - Input field for typing messages

- **Molecules:**
  - `ChatWindow` - Complete chat conversation area

- **Organisms:**
  - `FloatingChatbot` - Main floating chatbot widget

### Backend API

- **Chat Module** (`apps/api/src/modules/chat.module.ts`)
- **Chat Controller** (`apps/api/src/presentation/controllers/chat.controller.ts`)
- **Send Message Use Case** (`apps/api/src/application/use-cases/chat/send-message.use-case.ts`)

### State Management

- Uses Zustand store (`apps/web/store/chatStore.ts`) for managing chat state

## Setup Instructions

### 1. Environment Variables

#### API (.env)
Add your OpenAI API key to `apps/api/.env`:

```bash
OPENAI_API_KEY="your-openai-api-key-here"
```

#### Web App (.env.local)
The API URL is already configured:

```bash
NEXT_PUBLIC_API_URL="http://localhost:4000"
```

### 2. Get OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key and add it to your API .env file

### 3. Start the Application

```bash
# Start both API and web servers
pnpm dev

# Or start them separately:
# Terminal 1 - API
cd apps/api && pnpm dev

# Terminal 2 - Web
cd apps/web && pnpm dev
```

### 4. Test the Chatbot

1. Open your browser to `http://localhost:3000`
2. Look for the chat icon in the bottom-left corner
3. Click it to open the chat window
4. Start chatting!

You can also visit the test page: `http://localhost:3000/en/chatbot-test`

## Features

- **Floating Design**: Always accessible from bottom-left corner
- **Responsive**: Works on desktop and mobile
- **Conversation History**: Maintains context during the session
- **Error Handling**: Shows user-friendly error messages
- **Loading States**: Visual feedback during API calls
- **Customizable**: Easy to modify styling and behavior

## Customization

### Styling
The chatbot uses Tailwind CSS classes and follows your app's design system. You can customize:

- Colors by modifying the CSS variables in `apps/web/app/globals.css`
- Size and position in `FloatingChatbot` component
- Chat bubble styles in `ChatMessage` component

### AI Behavior
Modify the system prompt in `apps/api/src/application/use-cases/chat/send-message.use-case.ts`:

```typescript
{
  role: 'system',
  content: `Your custom system prompt here...`
}
```

### API Configuration
Change the OpenAI model or parameters in the same file:

```typescript
const completion = await this.openai.chat.completions.create({
  model: 'gpt-4', // Change model here
  messages,
  max_tokens: 500, // Adjust response length
  temperature: 0.7, // Adjust creativity
});
```

## Troubleshooting

### Common Issues

1. **Chat button appears but no response**
   - Check if OPENAI_API_KEY is set correctly
   - Verify API server is running on port 4000
   - Check browser console for errors

2. **"Failed to send message" error**
   - Verify API server is accessible
   - Check CORS configuration in API
   - Ensure OpenAI API key has sufficient credits

3. **TypeScript errors**
   - Run `pnpm check-types` to identify issues
   - Ensure all imports are correct

### Logs

Check API logs for detailed error information:
```bash
# API logs will show OpenAI API errors
cd apps/api && pnpm dev
```

## Next Steps

Consider adding these enhancements:

1. **Conversation Persistence**: Save chat history to database
2. **User Authentication**: Link chats to user accounts
3. **Custom Knowledge Base**: Train on your app's specific content
4. **Analytics**: Track chat usage and user satisfaction
5. **Multi-language Support**: Support different languages
6. **Voice Input**: Add speech-to-text functionality

## File Structure

```
apps/
├── api/
│   └── src/
│       ├── application/use-cases/chat/
│       ├── modules/chat.module.ts
│       └── presentation/controllers/chat.controller.ts
└── web/
    ├── components/
    │   ├── atoms/
    │   │   ├── ChatMessage/
    │   │   └── ChatInput/
    │   ├── molecules/
    │   │   └── ChatWindow/
    │   └── organisms/
    │       └── FloatingChatbot/
    └── store/chatStore.ts
```
